//! Meteora 账号数据 Anchor 解析器实现
//!
//! 使用简单直接的方法，避免复杂的宏系统

use async_trait::async_trait;
use anchor_lang::prelude::*;
use solana_sdk::pubkey::Pubkey;
use shared::Result;
use std::collections::HashMap;

use crate::accounts::traits::{
    AccountParser, AccountType, ParsedAccount, AccountData
};
use crate::anchor_types::meteora::{
    METEORA_DLMM_PROGRAM_ID, LbPair, BinArray, BinArrayBitmapExtension,
    Oracle, Position, PositionV2
};

/// Meteora Anchor 账号解析器
pub struct MeteoraAnchorAccountParser {
    name: String,
    discriminator_map: HashMap<[u8; 8], AccountType>,
}

impl MeteoraAnchorAccountParser {
    /// 创建新的 Meteora Anchor 账号解析器
    pub fn new() -> Self {
        let mut discriminator_map = HashMap::new();

        // 根据 Anchor discriminator 计算规则进行映射
        discriminator_map.insert([33, 11, 49, 98, 181, 101, 177, 13], AccountType::MeteoraLbPair);
        discriminator_map.insert([92, 142, 92, 220, 5, 148, 70, 181], AccountType::MeteoraBinArray);
        discriminator_map.insert([80, 111, 124, 113, 55, 237, 18, 5], AccountType::MeteoraBinArrayBitmapExtension);
        discriminator_map.insert([139, 194, 131, 179, 140, 179, 229, 244], AccountType::MeteoraOracle);
        discriminator_map.insert([170, 188, 143, 228, 122, 64, 247, 208], AccountType::MeteoraPosition);
        discriminator_map.insert([117, 176, 212, 199, 245, 180, 133, 182], AccountType::MeteoraPositionV2);

        Self {
            name: "MeteoraAnchorAccountParser".to_string(),
            discriminator_map,
        }
    }

    /// 使用 discriminator 识别账号类型
    fn identify_account_type_by_discriminator(&self, data: &[u8]) -> Option<AccountType> {
        if data.len() < 8 {
            return None;
        }

        let discriminator: [u8; 8] = data[0..8].try_into().ok()?;
        self.discriminator_map.get(&discriminator).cloned()
    }

    /// 解析 LbPair 账号
    fn parse_lb_pair(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let lb_pair = LbPair::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize LbPair: {}", e)))?;

        Ok(Box::new(MeteoraLbPairAdapter {
            address,
            inner: lb_pair,
        }))
    }

    /// 解析 BinArray 账号
    fn parse_bin_array(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let bin_array = BinArray::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize BinArray: {}", e)))?;

        Ok(Box::new(MeteoraBinArrayAdapter {
            address,
            inner: bin_array,
        }))
    }

    /// 解析 BinArrayBitmapExtension 账号
    fn parse_bin_array_bitmap_extension(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let bitmap_extension = BinArrayBitmapExtension::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize BinArrayBitmapExtension: {}", e)))?;

        Ok(Box::new(MeteoraBinArrayBitmapExtensionAdapter {
            address,
            inner: bitmap_extension,
        }))
    }

    /// 解析 Oracle 账号
    fn parse_oracle(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let oracle = Oracle::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize Oracle: {}", e)))?;

        Ok(Box::new(MeteoraOracleAdapter {
            address,
            inner: oracle,
        }))
    }

    /// 解析 Position 账号
    fn parse_position(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let position = Position::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize Position: {}", e)))?;

        Ok(Box::new(MeteoraPositionAdapter {
            address,
            inner: position,
        }))
    }

    /// 解析 PositionV2 账号
    fn parse_position_v2(&self, address: Pubkey, data: &[u8]) -> Result<Box<dyn AccountData>> {
        let position_v2 = PositionV2::deserialize(&mut &data[8..])
            .map_err(|e| shared::EchoesError::Parse(format!("Failed to deserialize PositionV2: {}", e)))?;

        Ok(Box::new(MeteoraPositionV2Adapter {
            address,
            inner: position_v2,
        }))
    }
}

impl Default for MeteoraAnchorAccountParser {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl AccountParser for MeteoraAnchorAccountParser {
    fn name(&self) -> &str {
        &self.name
    }

    fn supported_program_ids(&self) -> Vec<Pubkey> {
        vec![METEORA_DLMM_PROGRAM_ID]
    }

    fn supported_account_types(&self) -> Vec<AccountType> {
        vec![
            AccountType::MeteoraLbPair,
            AccountType::MeteoraBinArray,
            AccountType::MeteoraBinArrayBitmapExtension,
            AccountType::MeteoraOracle,
            AccountType::MeteoraPosition,
            AccountType::MeteoraPositionV2,
        ]
    }

    fn can_parse(&self, program_id: &Pubkey, account_data: &[u8]) -> bool {
        *program_id == METEORA_DLMM_PROGRAM_ID &&
        self.identify_account_type_by_discriminator(account_data).is_some()
    }

    fn identify_account_type(&self, program_id: &Pubkey, account_data: &[u8]) -> Option<AccountType> {
        if *program_id != METEORA_DLMM_PROGRAM_ID {
            return None;
        }

        self.identify_account_type_by_discriminator(account_data)
    }

    async fn parse_account(
        &self,
        address: Pubkey,
        program_id: Pubkey,
        account_data: &[u8],
    ) -> Result<ParsedAccount> {
        if program_id != METEORA_DLMM_PROGRAM_ID {
            return Err(shared::EchoesError::Parse(
                format!("Unsupported program ID: {}", program_id)
            ));
        }

        let account_type = self.identify_account_type_by_discriminator(account_data)
            .ok_or_else(|| shared::EchoesError::Parse(
                format!("Cannot identify account type for data length: {}", account_data.len())
            ))?;

        let parsed_data = match account_type {
            AccountType::MeteoraLbPair => {
                self.parse_lb_pair(address, account_data)?
            }
            AccountType::MeteoraBinArray => {
                self.parse_bin_array(address, account_data)?
            }
            AccountType::MeteoraBinArrayBitmapExtension => {
                self.parse_bin_array_bitmap_extension(address, account_data)?
            }
            AccountType::MeteoraOracle => {
                self.parse_oracle(address, account_data)?
            }
            AccountType::MeteoraPosition => {
                self.parse_position(address, account_data)?
            }
            AccountType::MeteoraPositionV2 => {
                self.parse_position_v2(address, account_data)?
            }
            _ => {
                return Err(shared::EchoesError::Parse(
                    format!("Unsupported account type: {:?}", account_type)
                ));
            }
        };

        let parsed_account = ParsedAccount::new(
            address,
            program_id,
            account_type,
            parsed_data,
            account_data.len(),
        );

        // 验证解析结果
        self.validate_parsed_data(&parsed_account)?;

        Ok(parsed_account)
    }
}

// 适配器结构体，将 Anchor 类型适配到现有的 AccountData trait
/// LbPair 适配器
#[derive(Debug)]
pub struct MeteoraLbPairAdapter {
    pub address: Pubkey,
    pub inner: LbPair,
}

impl AccountData for MeteoraLbPairAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::MeteoraLbPair
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        METEORA_DLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        1544 // 根据 IDL 定义的大小
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "active_id": self.inner.active_id,
            "bin_step": self.inner.bin_step,
            "status": self.inner.status,
            "token_x_mint": self.inner.token_x_mint.to_string(),
            "token_y_mint": self.inner.token_y_mint.to_string(),
            "reserve_x": self.inner.reserve_x.to_string(),
            "reserve_y": self.inner.reserve_y.to_string(),
            "protocol_fee_x": self.inner.protocol_fee.amount_x,
            "protocol_fee_y": self.inner.protocol_fee.amount_y,
            "oracle": self.inner.oracle.to_string(),
            "last_updated_at": self.inner.last_updated_at,
            "creator": self.inner.creator.to_string(),
        })
    }

    fn creator(&self) -> Option<Pubkey> {
        Some(self.inner.creator)
    }

    fn status(&self) -> Option<u8> {
        Some(self.inner.status)
    }

    fn associated_mints(&self) -> Vec<Pubkey> {
        vec![self.inner.token_x_mint, self.inner.token_y_mint]
    }
}

/// BinArray 适配器
#[derive(Debug)]
pub struct MeteoraBinArrayAdapter {
    pub address: Pubkey,
    pub inner: BinArray,
}

impl AccountData for MeteoraBinArrayAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::MeteoraBinArray
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        METEORA_DLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        8 + 1 + 7 + 32 + 70 * 128 // 基础结构 + 70个bin * 每个bin大小
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "index": self.inner.index,
            "version": self.inner.version,
            "lb_pair": self.inner.lb_pair.to_string(),
        })
    }
}

/// BinArrayBitmapExtension 适配器
#[derive(Debug)]
pub struct MeteoraBinArrayBitmapExtensionAdapter {
    pub address: Pubkey,
    pub inner: BinArrayBitmapExtension,
}

impl AccountData for MeteoraBinArrayBitmapExtensionAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::MeteoraBinArrayBitmapExtension
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        METEORA_DLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        32 + 12 * 8 * 8 + 12 * 8 * 8 // LB Pair + 两个位图
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "lb_pair": self.inner.lb_pair.to_string(),
        })
    }
}

/// Oracle 适配器
#[derive(Debug)]
pub struct MeteoraOracleAdapter {
    pub address: Pubkey,
    pub inner: Oracle,
}

impl AccountData for MeteoraOracleAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::MeteoraOracle
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        METEORA_DLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        8 + 8 + 8 // idx + active_size + length
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "idx": self.inner.idx,
            "active_size": self.inner.active_size,
            "length": self.inner.length,
        })
    }
}

/// Position 适配器
#[derive(Debug)]
pub struct MeteoraPositionAdapter {
    pub address: Pubkey,
    pub inner: Position,
}

impl AccountData for MeteoraPositionAdapter {
    fn account_type(&self) -> AccountType {
        AccountType::MeteoraPosition
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        METEORA_DLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        32 + 32 + 70 * 8 + 70 * 24 + 70 * 32 + 4 + 4 + 8 + 32 + 8 + 1 + 87 // 复杂结构，包含多个数组
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "lb_pair": self.inner.lb_pair.to_string(),
            "owner": self.inner.owner.to_string(),
            "lower_bin_id": self.inner.lower_bin_id,
            "upper_bin_id": self.inner.upper_bin_id,
            "last_updated_at": self.inner.last_updated_at,
            "total_claimed_fee_x_amount": self.inner.total_claimed_fee_x_amount,
            "total_claimed_fee_y_amount": self.inner.total_claimed_fee_y_amount,
            "total_claimed_rewards": self.inner.total_claimed_rewards,
        })
    }

    fn creator(&self) -> Option<Pubkey> {
        Some(self.inner.owner)
    }
}

/// PositionV2 适配器
#[derive(Debug)]
pub struct MeteoraPositionV2Adapter {
    pub address: Pubkey,
    pub inner: PositionV2,
}

impl AccountData for MeteoraPositionV2Adapter {
    fn account_type(&self) -> AccountType {
        AccountType::MeteoraPositionV2
    }

    fn address(&self) -> Pubkey {
        self.address
    }

    fn program_id(&self) -> Pubkey {
        METEORA_DLMM_PROGRAM_ID
    }

    fn data_size(&self) -> usize {
        32 + 32 + 70 * 16 + 70 * 24 + 70 * 32 + 4 + 4 + 8 + 32 + 8 + 1 + 87 // 复杂结构，包含多个数组（使用u128）
    }

    fn to_json(&self) -> serde_json::Value {
        serde_json::json!({
            "address": self.address.to_string(),
            "lb_pair": self.inner.lb_pair.to_string(),
            "owner": self.inner.owner.to_string(),
            "lower_bin_id": self.inner.lower_bin_id,
            "upper_bin_id": self.inner.upper_bin_id,
            "last_updated_at": self.inner.last_updated_at,
            "operator": self.inner.operator.to_string(),
            "lock_release_point": self.inner.lock_release_point,
            "total_claimed_fee_x_amount": self.inner.total_claimed_fee_x_amount,
            "total_claimed_fee_y_amount": self.inner.total_claimed_fee_y_amount,
            "total_claimed_rewards": self.inner.total_claimed_rewards,
            "fee_owner": self.inner.fee_owner.to_string(),
        })
    }

    fn creator(&self) -> Option<Pubkey> {
        Some(self.inner.owner)
    }
}

