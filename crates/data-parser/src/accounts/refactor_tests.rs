//! 重构后的代码测试
//!
//! 验证重构后的解析器功能正确性

#[cfg(test)]
mod tests {
    use super::*;
    use crate::accounts::traits::{AccountParser, AccountType};
    use crate::accounts::raydium::RaydiumAnchorAccountParser;
    use crate::accounts::meteora::MeteoraAnchorAccountParser;
    use crate::accounts::manager::AnchorParserManager;
    use crate::anchor_types::raydium::RAYDIUM_CLMM_PROGRAM_ID;
    use crate::anchor_types::meteora::METEORA_DLMM_PROGRAM_ID;
    use solana_sdk::pubkey::Pubkey;

    #[test]
    fn test_raydium_parser_creation() {
        let parser = RaydiumAnchorAccountParser::new();
        assert_eq!(parser.name(), "RaydiumAnchorAccountParser");

        let supported_programs = parser.supported_program_ids();
        assert!(supported_programs.contains(&RAYDIUM_CLMM_PROGRAM_ID));

        let supported_types = parser.supported_account_types();
        assert!(supported_types.contains(&AccountType::RaydiumPoolState));
        assert!(supported_types.contains(&AccountType::RaydiumTickArrayState));
        assert!(supported_types.contains(&AccountType::RaydiumPersonalPosition));
    }

    #[test]
    fn test_meteora_parser_creation() {
        let parser = MeteoraAnchorAccountParser::new();
        assert_eq!(parser.name(), "MeteoraAnchorAccountParser");

        let supported_programs = parser.supported_program_ids();
        assert!(supported_programs.contains(&METEORA_DLMM_PROGRAM_ID));

        let supported_types = parser.supported_account_types();
        assert!(supported_types.contains(&AccountType::MeteoraLbPair));
        assert!(supported_types.contains(&AccountType::MeteoraBinArray));
        assert!(supported_types.contains(&AccountType::MeteoraPosition));
    }

    #[test]
    fn test_manager_creation() {
        let manager = AnchorParserManager::new();
        assert_eq!(manager.name(), "AnchorParserManager");

        let supported_programs = manager.supported_program_ids();
        assert!(supported_programs.contains(&RAYDIUM_CLMM_PROGRAM_ID));
        assert!(supported_programs.contains(&METEORA_DLMM_PROGRAM_ID));

        let supported_types = manager.supported_account_types();
        assert!(supported_types.contains(&AccountType::RaydiumPoolState));
        assert!(supported_types.contains(&AccountType::MeteoraLbPair));
    }

    #[test]
    fn test_discriminator_identification() {
        let raydium_parser = RaydiumAnchorAccountParser::new();
        let meteora_parser = MeteoraAnchorAccountParser::new();

        // 测试 Raydium PoolState discriminator
        let pool_state_discriminator = [247, 237, 227, 245, 215, 195, 222, 70];
        let mut test_data = vec![0u8; 100];
        test_data[0..8].copy_from_slice(&pool_state_discriminator);

        let account_type = raydium_parser.identify_account_type(&RAYDIUM_CLMM_PROGRAM_ID, &test_data);
        assert_eq!(account_type, Some(AccountType::RaydiumPoolState));

        // 测试 Meteora LbPair discriminator
        let lb_pair_discriminator = [33, 11, 49, 98, 181, 101, 177, 13];
        test_data[0..8].copy_from_slice(&lb_pair_discriminator);

        let account_type = meteora_parser.identify_account_type(&METEORA_DLMM_PROGRAM_ID, &test_data);
        assert_eq!(account_type, Some(AccountType::MeteoraLbPair));
    }

    #[test]
    fn test_can_parse_functionality() {
        let manager = AnchorParserManager::new();

        // 测试 Raydium 数据
        let pool_state_discriminator = [247, 237, 227, 245, 215, 195, 222, 70];
        let mut raydium_data = vec![0u8; 100];
        raydium_data[0..8].copy_from_slice(&pool_state_discriminator);

        assert!(manager.can_parse(&RAYDIUM_CLMM_PROGRAM_ID, &raydium_data));
        assert!(!manager.can_parse(&METEORA_DLMM_PROGRAM_ID, &raydium_data));

        // 测试 Meteora 数据
        let lb_pair_discriminator = [33, 11, 49, 98, 181, 101, 177, 13];
        let mut meteora_data = vec![0u8; 100];
        meteora_data[0..8].copy_from_slice(&lb_pair_discriminator);

        assert!(manager.can_parse(&METEORA_DLMM_PROGRAM_ID, &meteora_data));
        assert!(!manager.can_parse(&RAYDIUM_CLMM_PROGRAM_ID, &meteora_data));

        // 测试未知数据
        let unknown_data = vec![0u8; 100];
        assert!(!manager.can_parse(&RAYDIUM_CLMM_PROGRAM_ID, &unknown_data));
        assert!(!manager.can_parse(&METEORA_DLMM_PROGRAM_ID, &unknown_data));
    }

    #[test]
    fn test_unsupported_program_id() {
        let manager = AnchorParserManager::new();
        let unknown_program_id = Pubkey::new_unique();
        let test_data = vec![0u8; 100];

        assert!(!manager.can_parse(&unknown_program_id, &test_data));
        assert_eq!(manager.identify_account_type(&unknown_program_id, &test_data), None);
    }

    #[test]
    fn test_insufficient_data() {
        let manager = AnchorParserManager::new();
        let short_data = vec![0u8; 4]; // 少于 8 字节的 discriminator

        assert!(!manager.can_parse(&RAYDIUM_CLMM_PROGRAM_ID, &short_data));
        assert!(!manager.can_parse(&METEORA_DLMM_PROGRAM_ID, &short_data));
    }

    #[test]
    fn test_manager_extensibility() {
        let mut manager = AnchorParserManager::new();
        let initial_count = manager.supported_program_ids().len();

        // 添加一个新的解析器（这里我们重复添加 Raydium 解析器作为示例）
        let additional_parser = std::sync::Arc::new(RaydiumAnchorAccountParser::new());
        manager.add_parser(additional_parser);

        // 验证解析器数量增加了
        let new_count = manager.supported_program_ids().len();
        assert!(new_count >= initial_count);
    }

    #[test]
    fn test_account_type_coverage() {
        let raydium_parser = RaydiumAnchorAccountParser::new();
        let meteora_parser = MeteoraAnchorAccountParser::new();

        // 验证 Raydium 支持的账号类型
        let raydium_types = raydium_parser.supported_account_types();
        let expected_raydium_types = vec![
            AccountType::RaydiumPoolState,
            AccountType::RaydiumTickArrayState,
            AccountType::RaydiumTickArrayBitmapExtension,
            AccountType::RaydiumObservationState,
            AccountType::RaydiumPersonalPosition,
            AccountType::RaydiumProtocolPosition,
        ];

        for expected_type in expected_raydium_types {
            assert!(raydium_types.contains(&expected_type));
        }

        // 验证 Meteora 支持的账号类型
        let meteora_types = meteora_parser.supported_account_types();
        let expected_meteora_types = vec![
            AccountType::MeteoraLbPair,
            AccountType::MeteoraBinArray,
            AccountType::MeteoraBinArrayBitmapExtension,
            AccountType::MeteoraOracle,
            AccountType::MeteoraPosition,
            AccountType::MeteoraPositionV2,
        ];

        for expected_type in expected_meteora_types {
            assert!(meteora_types.contains(&expected_type));
        }
    }
}
