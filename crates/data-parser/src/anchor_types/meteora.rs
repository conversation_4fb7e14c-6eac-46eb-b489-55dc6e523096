//! Meteora DLMM Anchor 类型定义
//!
//! 基于 meteora_dlmm.json IDL 文件生成的类型定义

use anchor_lang::prelude::*;

/// Meteora DLMM 程序ID
pub const METEORA_DLMM_PROGRAM_ID: Pubkey =
    solana_sdk::pubkey!("LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo");

/// 静态参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct StaticParameters {
    /// 基础因子
    pub base_factor: u16,
    /// 过滤周期
    pub filter_period: u16,
    /// 衰减周期
    pub decay_period: u16,
    /// 减少因子
    pub reduction_factor: u16,
    /// 可变费用控制
    pub variable_fee_control: u32,
    /// 最大波动性累积器
    pub max_volatility_accumulator: u32,
    /// 最小bin ID
    pub min_bin_id: i32,
    /// 最大bin ID
    pub max_bin_id: i32,
    /// 协议份额
    pub protocol_share: u16,
    /// 基础费用幂因子
    pub base_fee_power_factor: u8,
    /// 填充字段
    pub padding: [u8; 5],
}

/// 可变参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct VariableParameters {
    /// 波动性累积器
    pub volatility_accumulator: u32,
    /// 波动性参考
    pub volatility_reference: u32,
    /// 索引参考
    pub index_reference: i32,
    /// 填充字段
    pub padding: [u8; 4],
    /// 最后更新时间戳
    pub last_update_timestamp: i64,
    /// 填充字段1
    pub padding1: [u8; 8],
}

/// Bin 结构
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Bin {
    /// 代币X数量
    pub amount_x: u64,
    /// 代币Y数量
    pub amount_y: u64,
    /// Bin价格
    pub price: u128,
    /// 流动性供应
    pub liquidity_supply: u128,
    /// 每代币存储的奖励
    pub reward_per_token_stored: [u128; 2],
    /// 每代币存储的费用金额X
    pub fee_amount_x_per_token_stored: u128,
    /// 每代币存储的费用金额Y
    pub fee_amount_y_per_token_stored: u128,
    /// 累积代币X费用
    pub amount_x_in: u128,
    /// 累积代币Y费用
    pub amount_y_in: u128,
}

/// 奖励信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct RewardInfo {
    /// 奖励代币mint
    pub mint: Pubkey,
    /// 奖励金库
    pub vault: Pubkey,
    /// 资助者
    pub funder: Pubkey,
    /// 奖励持续时间
    pub reward_duration: u64,
    /// 奖励持续时间结束
    pub reward_duration_end: u64,
    /// 奖励率
    pub reward_rate: u128,
    /// 最后更新时间
    pub last_update_time: u64,
    /// 空流动性奖励累积秒数
    pub cumulative_seconds_with_empty_liquidity_reward: u64,
}

/// 用户奖励信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct UserRewardInfo {
    /// 每代币奖励完成数组
    pub reward_per_token_completes: [u128; 2],
    /// 待处理奖励数组
    pub reward_pendings: [u64; 2],
}

/// 费用信息
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct FeeInfo {
    /// 每代币费用X完成
    pub fee_x_per_token_complete: u128,
    /// 每代币费用Y完成
    pub fee_y_per_token_complete: u128,
    /// 费用X待处理
    pub fee_x_pending: u64,
    /// 费用Y待处理
    pub fee_y_pending: u64,
}

/// Meteora LB Pair
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct LbPair {
    /// 静态参数
    pub parameters: StaticParameters,
    /// 可变参数
    pub v_parameters: VariableParameters,
    /// Bump种子
    pub bump_seed: [u8; 1],
    /// Bin步长种子
    pub bin_step_seed: [u8; 2],
    /// 配对类型
    pub pair_type: u8,
    /// 活跃ID
    pub active_id: i32,
    /// Bin步长
    pub bin_step: u16,
    /// 状态
    pub status: u8,
    /// 是否需要基础因子种子
    pub require_base_factor_seed: u8,
    /// 基础因子种子
    pub base_factor_seed: [u8; 2],
    /// 激活类型
    pub activation_type: u8,
    /// 创建者池开关控制
    pub creator_pool_on_off_control: u8,
    /// 代币X mint
    pub token_x_mint: Pubkey,
    /// 代币Y mint
    pub token_y_mint: Pubkey,
    /// 储备X
    pub reserve_x: Pubkey,
    /// 储备Y
    pub reserve_y: Pubkey,
    /// 填充字段1 (之前的费用所有者，重用时需小心墓碑)
    pub padding1: [u8; 32],
    /// 协议费用
    pub protocol_fee: ProtocolFee,
    /// 奖励信息
    pub reward_infos: [RewardInfo; 2],
    /// Oracle
    pub oracle: Pubkey,
    /// Bin数组位图
    pub bin_array_bitmap: [u64; 16],
    /// 最后更新时间
    pub last_updated_at: i64,
    /// 填充字段2 (之前的白名单钱包，重用时需小心墓碑)
    pub padding2: [u8; 32],
    /// 预激活交换地址
    pub pre_activation_swap_address: Pubkey,
    /// 基础密钥
    pub base_key: Pubkey,
    /// 激活点
    pub activation_point: u64,
    /// 预激活持续时间
    pub pre_activation_duration: u64,
    /// 填充字段3 (之前的swap_cap_deactivate_point和swap_cap_amount，重用时需小心墓碑)
    pub padding3: [u8; 8],
    /// 填充字段4 (之前的lock_duration，重用时需小心墓碑)
    pub padding4: u64,
    /// 创建者
    pub creator: Pubkey,
    /// 代币X mint程序标志
    pub token_mint_x_program_flag: u8,
    /// 代币Y mint程序标志
    pub token_mint_y_program_flag: u8,
    /// 保留字段
    pub reserved: [u8; 22],
}

/// Meteora Bin Array
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BinArray {
    /// 索引
    pub index: i64,
    /// 版本
    pub version: u8,
    /// 填充字段
    pub padding: [u8; 7],
    /// LB Pair
    pub lb_pair: Pubkey,
    /// Bins数组
    pub bins: [Bin; 70],
}

#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ProtocolFee {
    pub amount_x: u64,
    pub amount_y: u64,
}

/// Meteora Bin Array Bitmap Extension
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct BinArrayBitmapExtension {
    /// LB Pair
    pub lb_pair: Pubkey,
    /// 正向bin数组位图
    pub positive_bin_array_bitmap: [[u64; 8]; 12],
    /// 负向bin数组位图
    pub negative_bin_array_bitmap: [[u64; 8]; 12],
}

/// Meteora Oracle
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Oracle {
    /// 最新观察索引
    pub idx: u64,
    /// 活跃大小
    pub active_size: u64,
    /// 长度
    pub length: u64,
}

/// Meteora Position
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct Position {
    /// LB Pair
    pub lb_pair: Pubkey,
    /// 所有者
    pub owner: Pubkey,
    /// 流动性份额
    pub liquidity_shares: [u64; 70],
    /// 奖励信息
    pub reward_infos: [UserRewardInfo; 70],
    /// 费用信息
    pub fee_infos: [FeeInfo; 70],
    /// 下边界bin ID
    pub lower_bin_id: i32,
    /// 上边界bin ID
    pub upper_bin_id: i32,
    /// 最后更新时间
    pub last_updated_at: i64,
    /// 总已领取费用X数量
    pub total_claimed_fee_x_amount: u64,
    /// 总已领取费用Y数量
    pub total_claimed_fee_y_amount: u64,
    /// 总已领取奖励
    pub total_claimed_rewards: [u64; 2],
    /// 保留字段
    pub reserved: [u8; 160],
}

/// Meteora Position V2
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PositionV2 {
    /// LB Pair
    pub lb_pair: Pubkey,
    /// 所有者
    pub owner: Pubkey,
    /// 流动性份额（使用u128）
    pub liquidity_shares: [u128; 70],
    /// 奖励信息
    pub reward_infos: [UserRewardInfo; 70],
    /// 费用信息
    pub fee_infos: [FeeInfo; 70],
    /// 下边界bin ID
    pub lower_bin_id: i32,
    /// 上边界bin ID
    pub upper_bin_id: i32,
    /// 最后更新时间
    pub last_updated_at: i64,
    /// 总已领取费用X数量
    pub total_claimed_fee_x_amount: u64,
    /// 总已领取费用Y数量
    pub total_claimed_fee_y_amount: u64,
    /// 总已领取奖励
    pub total_claimed_rewards: [u64; 2],
    /// 操作员
    pub operator: Pubkey,
    /// 锁定释放点
    pub lock_release_point: u64,
    /// 填充字段0 (之前的subjected_to_bootstrap_liquidity_locking，重用时需小心墓碑)
    pub padding0: u8,
    /// 费用所有者
    pub fee_owner: Pubkey,
    /// 保留字段
    pub reserved: [u8; 87],
}

/// 预设参数
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PresetParameter {
    /// Bin步长
    pub bin_step: u16,
    /// 基础因子
    pub base_factor: u16,
    /// 过滤周期
    pub filter_period: u16,
    /// 衰减周期
    pub decay_period: u16,
    /// 减少因子
    pub reduction_factor: u16,
    /// 可变费用控制
    pub variable_fee_control: u32,
    /// 最大波动性累积器
    pub max_volatility_accumulator: u32,
    /// 最小bin ID
    pub min_bin_id: i32,
    /// 最大bin ID
    pub max_bin_id: i32,
    /// 协议份额
    pub protocol_share: u16,
}

/// 预设参数2
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct PresetParameter2 {
    /// Bin步长
    pub bin_step: u16,
    /// 基础因子
    pub base_factor: u16,
    /// 过滤周期
    pub filter_period: u16,
    /// 衰减周期
    pub decay_period: u16,
    /// 可变费用控制
    pub variable_fee_control: u32,
    /// 最大波动性累积器
    pub max_volatility_accumulator: u32,
    /// 减少因子
    pub reduction_factor: u16,
    /// 协议份额
    pub protocol_share: u16,
    /// 索引
    pub index: u16,
    /// 基础费用幂因子
    pub base_fee_power_factor: u8,
    /// 填充字段0
    pub padding0: u8,
    /// 填充字段1
    pub padding1: [u64; 20],
}

/// 代币徽章
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct TokenBadge {
    /// 代币mint
    pub token_mint: Pubkey,
    /// 填充字段
    pub padding: [u8; 128],
}

/// 领取费用操作员
#[derive(AnchorSerialize, AnchorDeserialize, Clone, Debug)]
pub struct ClaimFeeOperator {
    /// 操作员
    pub operator: Pubkey,
    /// 填充字段
    pub padding: [u8; 128],
}
