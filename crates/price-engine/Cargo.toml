[package]
name = "price-engine"
version.workspace = true
edition.workspace = true

[dependencies]
# 工作区依赖
shared = { path = "../shared" }
state-manager = { path = "../state-manager" }
pool-registry = { path = "../pool-registry" }
data-parser = { path = "../data-parser" }

# 基础库
async-trait.workspace = true
futures.workspace = true
futures-util.workspace = true
serde.workspace = true
serde_json.workspace = true
solana-sdk.workspace = true
thiserror.workspace = true
tokio.workspace = true
tracing.workspace = true

# 数学计算
#num-traits = "0.2"
ordered-float = { version = "4.4", features = ["serde"] }

# 缓存和并发
dashmap = "6.1"
#papaya = "0.1"
arc-swap = "1.7"

# 时间和度量
chrono.workspace = true
once_cell = "1.20"
uuid.workspace = true
rand.workspace = true

# Redis缓存（可选）
redis = { workspace = true, features = ["tokio-comp", "connection-manager"], optional = true }

# 配置管理
#config.workspace = true
#yaml-rust2 = "0.8"

[features]
default = ["memory-cache"]
memory-cache = []
redis-cache = ["redis"]

[dev-dependencies]
tokio-test = "0.4"
approx = "0.5"
criterion = { version = "0.5", features = ["html_reports"] }
