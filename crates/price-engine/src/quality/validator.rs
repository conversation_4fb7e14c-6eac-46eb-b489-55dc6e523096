//! Price Validator Implementation
//!
//! 价格验证器实现，提供价格数据的验证和异常检测功能

use chrono::Utc;
use std::collections::VecDeque;
use tracing::{debug, warn};

use super::{PriceStatistics, QualityConfig, QualityError};
use crate::calculator::traits::{AnomalyResult, AnomalyType, PriceValidator, ValidationResult};
use crate::types::PriceQuote;

/// 默认价格验证器实现
#[derive(Debug)]
pub struct DefaultPriceValidator {
    /// 验证配置
    config: QualityConfig,
}

impl DefaultPriceValidator {
    /// 创建新的默认价格验证器
    pub fn new() -> Self {
        Self::with_config(QualityConfig::default())
    }

    /// 使用配置创建价格验证器
    pub fn with_config(config: QualityConfig) -> Self {
        Self { config }
    }

    /// 验证价格范围合理性
    fn validate_price_range(&self, quote: &PriceQuote) -> Result<(), String> {
        // 检查价格是否为正数
        if quote.spot_price <= 0.0 {
            return Err("现货价格必须为正数".to_string());
        }

        if quote.bid_price <= 0.0 {
            return Err("买入价格必须为正数".to_string());
        }

        if quote.ask_price <= 0.0 {
            return Err("卖出价格必须为正数".to_string());
        }

        // 检查买卖价差合理性
        if quote.bid_price >= quote.ask_price {
            return Err("买入价格不能高于或等于卖出价格".to_string());
        }

        // 检查现货价格是否在买卖价格之间
        if quote.spot_price < quote.bid_price || quote.spot_price > quote.ask_price {
            return Err("现货价格应该在买入价格和卖出价格之间".to_string());
        }

        // 检查价差是否过大
        let spread_percent = ((quote.ask_price - quote.bid_price) / quote.spot_price) * 100.0;
        if spread_percent > 10.0 { // 10%以上的价差可能异常
            return Err(format!("价差过大: {:.2}%", spread_percent));
        }

        Ok(())
    }

    /// 验证置信区间合理性
    fn validate_confidence_interval(&self, quote: &PriceQuote) -> Result<(), String> {
        let ci = &quote.confidence_interval;

        // 检查置信水平
        if ci.confidence_level <= 0.0 || ci.confidence_level > 1.0 {
            return Err("置信水平必须在0到1之间".to_string());
        }

        if ci.confidence_level < self.config.required_confidence_level {
            return Err(format!(
                "置信水平过低: {:.2}, 要求: {:.2}",
                ci.confidence_level, self.config.required_confidence_level
            ));
        }

        // 检查置信区间边界
        if ci.lower_bound >= ci.upper_bound {
            return Err("置信区间下界必须小于上界".to_string());
        }

        if ci.lower_bound <= 0.0 {
            return Err("置信区间下界必须为正数".to_string());
        }

        // 检查现货价格是否在置信区间内
        if quote.spot_price < ci.lower_bound || quote.spot_price > ci.upper_bound {
            return Err("现货价格不在置信区间内".to_string());
        }

        // 检查置信区间宽度是否合理
        let interval_width = (ci.upper_bound - ci.lower_bound) / quote.spot_price;
        if interval_width > 0.5 { // 50%以上的区间宽度可能过大
            return Err(format!("置信区间过宽: {:.2}%", interval_width * 100.0));
        }

        Ok(())
    }

    /// 验证数据新鲜度
    fn validate_freshness(&self, quote: &PriceQuote) -> Result<(), String> {
        let age_seconds = (Utc::now() - quote.timestamp).num_seconds();

        if age_seconds < 0 {
            return Err("价格时间戳不能是未来时间".to_string());
        }

        if age_seconds > self.config.min_freshness_seconds as i64 {
            return Err(format!(
                "数据过时: {}秒前, 要求: {}秒内",
                age_seconds, self.config.min_freshness_seconds
            ));
        }

        // 检查过期时间
        if quote.expires_at <= Utc::now() {
            return Err("价格数据已过期".to_string());
        }

        Ok(())
    }

    /// 验证市场数据完整性
    fn validate_market_data(&self, quote: &PriceQuote) -> Result<(), String> {
        // 检查质量评分
        if quote.quality_score < 0.0 || quote.quality_score > 100.0 {
            return Err("质量评分必须在0到100之间".to_string());
        }

        if quote.quality_score < self.config.min_quality_score {
            return Err(format!(
                "质量评分过低: {:.1}, 要求: {:.1}",
                quote.quality_score, self.config.min_quality_score
            ));
        }

        // 检查价差百分比
        if quote.spread_percent < 0.0 || quote.spread_percent > 100.0 {
            return Err("价差百分比必须在0到100之间".to_string());
        }

        // 检查24小时价格变化（如果存在）
        if let Some(price_change) = quote.price_change_24h {
            if price_change.abs() > 100.0 { // 超过100%的变化可能异常
                return Err(format!("24小时价格变化异常: {:.2}%", price_change));
            }
        }

        // 检查成交量（如果存在）
        if let Some(volume) = quote.volume_24h {
            if volume == 0 {
                return Err("24小时成交量不能为0".to_string());
            }
        }

        // 检查TVL（如果存在）
        if let Some(tvl) = quote.tvl {
            if tvl < self.config.min_liquidity_threshold as u64 {
                return Err(format!(
                    "TVL过低: {}, 要求: {}",
                    tvl, self.config.min_liquidity_threshold
                ));
            }
        }

        Ok(())
    }

    /// 计算验证置信度
    fn calculate_validation_confidence(&self, messages: &[String]) -> f64 {
        if messages.is_empty() {
            100.0
        } else {
            // 根据错误数量和严重性计算置信度
            let base_confidence = 100.0;
            let penalty_per_error = 100.0 / 10.0; // 假设最多10个错误对应0%置信度
            
            let penalty = messages.len() as f64 * penalty_per_error;
            (base_confidence - penalty).max(0.0)
        }
    }

    /// 计算质量评分
    fn calculate_quality_score(&self, quote: &PriceQuote, validation_errors: &[String]) -> f64 {
        let mut score = quote.quality_score;

        // 根据验证错误调整评分
        for error in validation_errors {
            if error.contains("过时") {
                score -= 20.0;
            } else if error.contains("置信") {
                score -= 15.0;
            } else if error.contains("价差") {
                score -= 10.0;
            } else if error.contains("异常") {
                score -= 25.0;
            } else {
                score -= 5.0;
            }
        }

        score.max(0.0)
    }

    /// 使用IQR方法检测异常值
    fn detect_outliers_iqr(&self, prices: &[f64], current_price: f64) -> bool {
        if prices.len() < 4 {
            return false; // 数据不足
        }

        let mut sorted_prices = prices.to_vec();
        sorted_prices.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let n = sorted_prices.len();
        let q1_index = n / 4;
        let q3_index = 3 * n / 4;

        let q1 = sorted_prices[q1_index];
        let q3 = sorted_prices[q3_index];
        let iqr = q3 - q1;

        let lower_bound = q1 - 1.5 * iqr;
        let upper_bound = q3 + 1.5 * iqr;

        current_price < lower_bound || current_price > upper_bound
    }

    /// 使用移动平均检测趋势异常
    fn detect_trend_anomaly(&self, prices: &[f64], current_price: f64, window_size: usize) -> bool {
        if prices.len() < window_size {
            return false;
        }

        let recent_prices = &prices[prices.len() - window_size..];
        let moving_average: f64 = recent_prices.iter().sum::<f64>() / recent_prices.len() as f64;
        
        let deviation = (current_price - moving_average).abs() / moving_average;
        deviation > self.config.max_price_deviation_percent / 100.0
    }

    /// 检测波动率异常
    fn detect_volatility_anomaly(&self, prices: &[f64]) -> bool {
        if prices.len() < 2 {
            return false;
        }

        match PriceStatistics::from_prices(prices) {
            Ok(stats) => {
                let volatility = stats.coefficient_of_variation * 100.0;
                volatility > self.config.max_volatility_percent
            }
            Err(_) => false,
        }
    }

    /// 检测价格跳跃
    fn detect_price_jumps(&self, prices: &[f64], current_price: f64) -> bool {
        if prices.is_empty() {
            return false;
        }

        let last_price = prices[prices.len() - 1];
        let price_change = (current_price - last_price).abs() / last_price;
        
        price_change > self.config.max_price_deviation_percent / 100.0
    }

    /// 检测成交量异常
    fn detect_volume_anomaly(&self, quote: &PriceQuote, historical_volumes: Option<&[u64]>) -> bool {
        if let (Some(current_volume), Some(hist_volumes)) = (quote.volume_24h, historical_volumes) {
            if hist_volumes.is_empty() {
                return false;
            }

            let avg_volume = hist_volumes.iter().sum::<u64>() as f64 / hist_volumes.len() as f64;
            let volume_ratio = current_volume as f64 / avg_volume;
            
            // 成交量异常：太高或太低
            volume_ratio > 5.0 || volume_ratio < 0.2
        } else {
            false
        }
    }

    /// 计算异常评分
    fn calculate_anomaly_score(&self, anomaly_flags: &[bool]) -> f64 {
        let anomaly_count = anomaly_flags.iter().filter(|&&flag| flag).count();
        let total_checks = anomaly_flags.len();
        
        if total_checks == 0 {
            0.0
        } else {
            (anomaly_count as f64 / total_checks as f64) * 100.0
        }
    }

    /// 确定异常类型
    fn determine_anomaly_type(
        &self, 
        price_outlier: bool,
        trend_anomaly: bool,
        volatility_anomaly: bool,
        price_jump: bool,
        volume_anomaly: bool
    ) -> Option<AnomalyType> {
        if price_jump && price_outlier {
            Some(AnomalyType::PriceSpike) // 可能是价格突变或暴跌
        } else if volatility_anomaly {
            Some(AnomalyType::UnusualVolatility)
        } else if volume_anomaly {
            Some(AnomalyType::UnusualVolatility) // 将成交量异常也归类为异常波动
        } else if price_outlier || trend_anomaly {
            Some(AnomalyType::PriceSpike)
        } else {
            None
        }
    }
}

impl Default for DefaultPriceValidator {
    fn default() -> Self {
        Self::new()
    }
}

impl PriceValidator for DefaultPriceValidator {
    /// 验证价格合理性
    fn validate_price(
        &self,
        quote: &PriceQuote,
        historical_data: Option<&[f64]>,
    ) -> Result<ValidationResult, crate::types::PriceEngineError> {
        let mut messages = Vec::new();
        let mut is_valid = true;

        // 1. 验证价格范围
        if let Err(msg) = self.validate_price_range(quote) {
            messages.push(msg);
            is_valid = false;
        }

        // 2. 验证置信区间
        if let Err(msg) = self.validate_confidence_interval(quote) {
            messages.push(msg);
            if self.config.strict_mode {
                is_valid = false;
            }
        }

        // 3. 验证数据新鲜度
        if let Err(msg) = self.validate_freshness(quote) {
            messages.push(msg);
            if self.config.strict_mode {
                is_valid = false;
            }
        }

        // 4. 验证市场数据
        if let Err(msg) = self.validate_market_data(quote) {
            messages.push(msg);
            if self.config.strict_mode {
                is_valid = false;
            }
        }

        // 5. 与历史数据比较（如果提供）
        if let Some(history) = historical_data {
            if history.len() >= self.config.min_sample_count {
                match PriceStatistics::from_prices(history) {
                    Ok(stats) => {
                        // 检查是否为异常值
                        if stats.is_outlier(quote.spot_price, self.config.anomaly_sensitivity) {
                            messages.push(format!(
                                "价格异常值: Z分数为 {:.2}",
                                stats.z_score(quote.spot_price)
                            ));
                            if self.config.strict_mode {
                                is_valid = false;
                            }
                        }

                        // 检查价格偏差
                        let deviation = (quote.spot_price - stats.mean).abs() / stats.mean * 100.0;
                        if deviation > self.config.max_price_deviation_percent {
                            messages.push(format!(
                                "价格偏差过大: {:.2}%",
                                deviation
                            ));
                            if self.config.strict_mode {
                                is_valid = false;
                            }
                        }
                    }
                    Err(e) => {
                        messages.push(format!("历史数据分析失败: {}", e));
                    }
                }
            } else {
                messages.push("历史数据样本不足".to_string());
            }
        }

        let confidence = self.calculate_validation_confidence(&messages);
        let quality_score = self.calculate_quality_score(quote, &messages);

        debug!(
            "价格验证完成: {} {} valid={} confidence={:.1} quality={:.1}",
            quote.token_pair, quote.source, is_valid, confidence, quality_score
        );

        Ok(ValidationResult {
            is_valid,
            confidence,
            messages,
            quality_score,
        })
    }

    /// 检测价格异常
    fn detect_anomaly(
        &self,
        current_price: f64,
        historical_prices: &[f64],
    ) -> Result<AnomalyResult, crate::types::PriceEngineError> {
        if historical_prices.is_empty() {
            return Ok(AnomalyResult {
                is_anomaly: false,
                anomaly_score: 0.0,
                anomaly_type: None,
                confidence_interval: (current_price * 0.95, current_price * 1.05),
            });
        }

        // 执行多种异常检测方法
        let price_outlier = self.detect_outliers_iqr(historical_prices, current_price);
        let trend_anomaly = self.detect_trend_anomaly(historical_prices, current_price, 5);
        let volatility_anomaly = self.detect_volatility_anomaly(historical_prices);
        let price_jump = self.detect_price_jumps(historical_prices, current_price);

        // 计算异常评分
        let anomaly_flags = vec![price_outlier, trend_anomaly, volatility_anomaly, price_jump];
        let anomaly_score = self.calculate_anomaly_score(&anomaly_flags);

        // 确定是否为异常
        let is_anomaly = anomaly_score > 25.0; // 25%以上的异常指标触发

        // 确定异常类型
        let anomaly_type = if is_anomaly {
            self.determine_anomaly_type(
                price_outlier,
                trend_anomaly, 
                volatility_anomaly,
                price_jump,
                false // 这里没有成交量数据
            )
        } else {
            None
        };

        // 计算置信区间
        let confidence_interval = if historical_prices.len() >= 3 {
            match PriceStatistics::from_prices(historical_prices) {
                Ok(stats) => {
                    let margin = stats.std_dev * 1.96; // 95%置信区间
                    (stats.mean - margin, stats.mean + margin)
                }
                Err(_) => (current_price * 0.9, current_price * 1.1),
            }
        } else {
            (current_price * 0.95, current_price * 1.05)
        };

        if is_anomaly {
            warn!(
                "检测到价格异常: price={:.4} score={:.1}% type={:?}",
                current_price, anomaly_score, anomaly_type
            );
        }

        Ok(AnomalyResult {
            is_anomaly,
            anomaly_score,
            anomaly_type,
            confidence_interval,
        })
    }
}

/// 高级价格验证器
/// 
/// 包含更复杂的验证逻辑和机器学习算法
#[derive(Debug)]
pub struct AdvancedPriceValidator {
    /// 基础验证器
    base_validator: DefaultPriceValidator,
    /// 价格历史缓存
    price_history: VecDeque<(f64, chrono::DateTime<Utc>)>,
    /// 最大历史长度
    max_history_length: usize,
}

impl AdvancedPriceValidator {
    /// 创建新的高级验证器
    pub fn new(max_history_length: usize) -> Self {
        Self {
            base_validator: DefaultPriceValidator::new(),
            price_history: VecDeque::with_capacity(max_history_length),
            max_history_length,
        }
    }

    /// 添加价格到历史记录
    pub fn add_price(&mut self, price: f64, timestamp: chrono::DateTime<Utc>) {
        if self.price_history.len() >= self.max_history_length {
            self.price_history.pop_front();
        }
        self.price_history.push_back((price, timestamp));
    }

    /// 获取历史价格
    pub fn get_historical_prices(&self) -> Vec<f64> {
        self.price_history.iter().map(|(price, _)| *price).collect()
    }

    /// 执行季节性分析
    fn analyze_seasonality(&self, _current_price: f64) -> bool {
        // 这里可以实现更复杂的季节性分析
        // 例如检测周期性模式、时间相关的价格变化等
        false
    }

    /// 执行相关性分析
    fn analyze_correlation(&self, _current_price: f64) -> bool {
        // 这里可以实现与其他市场指标的相关性分析
        false
    }
}

impl PriceValidator for AdvancedPriceValidator {
    fn validate_price(
        &self,
        quote: &PriceQuote,
        historical_data: Option<&[f64]>,
    ) -> Result<ValidationResult, crate::types::PriceEngineError> {
        // 首先使用基础验证器
        let mut result = self.base_validator.validate_price(quote, historical_data)?;

        // 添加高级验证逻辑
        let seasonality_anomaly = self.analyze_seasonality(quote.spot_price);
        let correlation_anomaly = self.analyze_correlation(quote.spot_price);

        if seasonality_anomaly {
            result.messages.push("检测到季节性异常".to_string());
            result.quality_score -= 5.0;
        }

        if correlation_anomaly {
            result.messages.push("检测到相关性异常".to_string());
            result.quality_score -= 5.0;
        }

        // 重新计算置信度
        result.confidence = self.base_validator.calculate_validation_confidence(&result.messages);
        result.quality_score = result.quality_score.max(0.0);

        Ok(result)
    }

    fn detect_anomaly(
        &self,
        current_price: f64,
        historical_prices: &[f64],
    ) -> Result<AnomalyResult, crate::types::PriceEngineError> {
        // 使用基础异常检测
        let mut result = self.base_validator.detect_anomaly(current_price, historical_prices)?;

        // 添加高级异常检测
        let seasonality_anomaly = self.analyze_seasonality(current_price);
        let correlation_anomaly = self.analyze_correlation(current_price);

        if seasonality_anomaly || correlation_anomaly {
            result.anomaly_score += 10.0;
            if result.anomaly_score > 50.0 {
                result.is_anomaly = true;
                if result.anomaly_type.is_none() {
                    result.anomaly_type = Some(AnomalyType::SystemError);
                }
            }
        }

        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{TokenPair, DexType, ConfidenceInterval};
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    fn create_test_quote(price: f64) -> PriceQuote {
        PriceQuote {
            token_pair: TokenPair::new(
                Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
                Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            ),
            spot_price: price,
            bid_price: price * 0.999,
            ask_price: price * 1.001,
            spread_percent: 0.2,
            price_change_24h: Some(5.5),
            volume_24h: Some(1000000),
            tvl: Some(50000000),
            source: DexType::RaydiumClmm,
            pool_id: Pubkey::default(),
            quality_score: 95.0,
            confidence_interval: ConfidenceInterval {
                confidence_level: 0.95,
                lower_bound: price * 0.95,
                upper_bound: price * 1.05,
            },
            timestamp: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::seconds(60),
        }
    }

    #[test]
    fn test_validator_creation() {
        let validator = DefaultPriceValidator::new();
        assert_eq!(validator.config.min_freshness_seconds, 60);
    }

    #[test]
    fn test_price_range_validation() {
        let validator = DefaultPriceValidator::new();
        
        // 正常价格
        let good_quote = create_test_quote(100.0);
        assert!(validator.validate_price_range(&good_quote).is_ok());
        
        // 负价格
        let mut bad_quote = create_test_quote(-100.0);
        assert!(validator.validate_price_range(&bad_quote).is_err());
        
        // 买卖价格倒置
        bad_quote = create_test_quote(100.0);
        bad_quote.bid_price = 101.0;
        bad_quote.ask_price = 99.0;
        assert!(validator.validate_price_range(&bad_quote).is_err());
    }

    #[test]
    fn test_confidence_interval_validation() {
        let validator = DefaultPriceValidator::new();
        
        // 正常置信区间
        let good_quote = create_test_quote(100.0);
        assert!(validator.validate_confidence_interval(&good_quote).is_ok());
        
        // 无效置信水平
        let mut bad_quote = create_test_quote(100.0);
        bad_quote.confidence_interval.confidence_level = 1.5;
        assert!(validator.validate_confidence_interval(&bad_quote).is_err());
        
        // 价格不在置信区间内
        bad_quote = create_test_quote(100.0);
        bad_quote.confidence_interval.lower_bound = 110.0;
        bad_quote.confidence_interval.upper_bound = 120.0;
        assert!(validator.validate_confidence_interval(&bad_quote).is_err());
    }

    #[test]
    fn test_freshness_validation() {
        let validator = DefaultPriceValidator::new();
        
        // 新鲜数据
        let fresh_quote = create_test_quote(100.0);
        assert!(validator.validate_freshness(&fresh_quote).is_ok());
        
        // 过时数据
        let mut stale_quote = create_test_quote(100.0);
        stale_quote.timestamp = Utc::now() - chrono::Duration::minutes(10);
        assert!(validator.validate_freshness(&stale_quote).is_err());
        
        // 未来时间
        let mut future_quote = create_test_quote(100.0);
        future_quote.timestamp = Utc::now() + chrono::Duration::minutes(10);
        assert!(validator.validate_freshness(&future_quote).is_err());
    }

    #[test]
    fn test_complete_validation() {
        let validator = DefaultPriceValidator::new();
        let quote = create_test_quote(100.0);
        let history = vec![98.0, 99.0, 100.0, 101.0, 102.0];
        
        let result = validator.validate_price(&quote, Some(&history)).unwrap();
        assert!(result.is_valid);
        assert!(result.confidence > 0.0);
        assert!(result.quality_score > 0.0);
    }

    #[test]
    fn test_outlier_detection_iqr() {
        let validator = DefaultPriceValidator::new();
        let normal_prices = vec![100.0, 101.0, 99.0, 102.0, 98.0, 103.0, 97.0];
        
        // 正常价格
        assert!(!validator.detect_outliers_iqr(&normal_prices, 100.0));
        
        // 异常价格
        assert!(validator.detect_outliers_iqr(&normal_prices, 150.0));
        assert!(validator.detect_outliers_iqr(&normal_prices, 50.0));
    }

    #[test]
    fn test_trend_anomaly_detection() {
        let validator = DefaultPriceValidator::new();
        let trending_prices = vec![100.0, 101.0, 102.0, 103.0, 104.0];
        
        // 符合趋势的价格
        assert!(!validator.detect_trend_anomaly(&trending_prices, 105.0, 5));
        
        // 偏离趋势的价格
        assert!(validator.detect_trend_anomaly(&trending_prices, 90.0, 5));
    }

    #[test]
    fn test_volatility_anomaly_detection() {
        let validator = DefaultPriceValidator::new();
        
        // 稳定价格序列
        let stable_prices = vec![100.0, 100.1, 99.9, 100.2, 99.8];
        assert!(!validator.detect_volatility_anomaly(&stable_prices));
        
        // 高波动价格序列
        let volatile_prices = vec![100.0, 150.0, 80.0, 120.0, 90.0];
        assert!(validator.detect_volatility_anomaly(&volatile_prices));
    }

    #[test]
    fn test_price_jump_detection() {
        let validator = DefaultPriceValidator::new();
        let prices = vec![100.0, 101.0, 99.0, 102.0];
        
        // 正常价格变化
        assert!(!validator.detect_price_jumps(&prices, 103.0));
        
        // 价格跳跃
        assert!(validator.detect_price_jumps(&prices, 150.0));
    }

    #[test]
    fn test_anomaly_detection() {
        let validator = DefaultPriceValidator::new();
        let history = vec![100.0, 101.0, 99.0, 102.0, 98.0];
        
        // 正常价格
        let normal_result = validator.detect_anomaly(100.0, &history).unwrap();
        assert!(!normal_result.is_anomaly);
        
        // 异常价格
        let anomaly_result = validator.detect_anomaly(150.0, &history).unwrap();
        assert!(anomaly_result.is_anomaly);
        assert!(anomaly_result.anomaly_score > 0.0);
        assert!(anomaly_result.anomaly_type.is_some());
    }

    #[test]
    fn test_advanced_validator() {
        let mut validator = AdvancedPriceValidator::new(10);
        
        // 添加一些历史价格
        for i in 95..105 {
            validator.add_price(i as f64, Utc::now());
        }
        
        let quote = create_test_quote(100.0);
        let history = validator.get_historical_prices();
        
        let result = validator.validate_price(&quote, Some(&history)).unwrap();
        assert!(result.is_valid);
        assert!(result.confidence > 0.0);
    }

    #[test]
    fn test_anomaly_score_calculation() {
        let validator = DefaultPriceValidator::new();
        
        // 没有异常
        let no_anomaly = vec![false, false, false, false];
        assert_eq!(validator.calculate_anomaly_score(&no_anomaly), 0.0);
        
        // 一半异常
        let half_anomaly = vec![true, false, true, false];
        assert_eq!(validator.calculate_anomaly_score(&half_anomaly), 50.0);
        
        // 全部异常
        let all_anomaly = vec![true, true, true, true];
        assert_eq!(validator.calculate_anomaly_score(&all_anomaly), 100.0);
    }
}