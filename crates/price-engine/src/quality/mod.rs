//! Price Quality Module
//!
//! 价格质量监控和验证模块，确保价格数据的准确性和可靠性

pub mod monitor;
pub mod validator;

pub use monitor::PriceQualityMonitor;
pub use validator::DefaultPriceValidator;
// PriceValidator trait 从 calculator::traits 重新导出
pub use crate::calculator::traits::PriceValidator;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

use crate::types::{DexType, PriceQuote, TokenPair};

/// 质量监控错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum QualityError {
    #[error("验证失败: {0}")]
    ValidationFailed(String),
    
    #[error("异常检测: {0}")]
    AnomalyDetected(String),
    
    #[error("数据不足: {0}")]
    InsufficientData(String),
    
    #[error("配置错误: {0}")]
    Configuration(String),
    
    #[error("统计计算错误: {0}")]
    StatisticsError(String),
    
    #[error("阈值超限: expected {expected}, got {actual}")]
    ThresholdExceeded { expected: f64, actual: f64 },
    
    #[error("内部错误: {0}")]
    Internal(String),
}

/// 质量监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityConfig {
    /// 最小数据新鲜度要求（秒）
    pub min_freshness_seconds: u64,
    /// 最大价格偏差百分比
    pub max_price_deviation_percent: f64,
    /// 最小流动性阈值
    pub min_liquidity_threshold: u128,
    /// 价格稳定性窗口大小
    pub stability_window_size: usize,
    /// 最大价格波动率（百分比）
    pub max_volatility_percent: f64,
    /// 异常检测敏感度
    pub anomaly_sensitivity: f64,
    /// 质量评分权重
    pub quality_weights: QualityWeights,
    /// 启用严格模式
    pub strict_mode: bool,
    /// 最小样本数量
    pub min_sample_count: usize,
    /// 置信区间要求
    pub required_confidence_level: f64,
    /// 最小质量评分阈值
    pub min_quality_score: f64,
}

impl Default for QualityConfig {
    fn default() -> Self {
        Self {
            min_freshness_seconds: 60,
            max_price_deviation_percent: 10.0,
            min_liquidity_threshold: 1_000_000,
            stability_window_size: 10,
            max_volatility_percent: 20.0,
            anomaly_sensitivity: 2.0,
            quality_weights: QualityWeights::default(),
            strict_mode: false,
            min_sample_count: 3,
            required_confidence_level: 0.90,
            min_quality_score: 70.0,
        }
    }
}

/// 质量评分权重
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityWeights {
    /// 数据新鲜度权重
    pub freshness_weight: f64,
    /// 流动性深度权重
    pub liquidity_weight: f64,
    /// 价格稳定性权重
    pub stability_weight: f64,
    /// 成交量权重
    pub volume_weight: f64,
    /// 数据源可靠性权重
    pub source_reliability_weight: f64,
}

impl Default for QualityWeights {
    fn default() -> Self {
        Self {
            freshness_weight: 0.25,
            liquidity_weight: 0.25,
            stability_weight: 0.20,
            volume_weight: 0.15,
            source_reliability_weight: 0.15,
        }
    }
}

/// 价格质量评估结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityAssessment {
    /// 代币对
    pub token_pair: TokenPair,
    /// DEX类型
    pub dex_type: DexType,
    /// 总体质量评分 (0-100)
    pub overall_score: f64,
    /// 详细评分
    pub detailed_scores: DetailedQualityScores,
    /// 质量标志
    pub quality_flags: Vec<QualityFlag>,
    /// 风险级别
    pub risk_level: RiskLevel,
    /// 建议
    pub recommendations: Vec<String>,
    /// 评估时间
    pub assessed_at: DateTime<Utc>,
    /// 数据样本数量
    pub sample_count: usize,
    /// 置信度
    pub confidence: f64,
}

/// 详细质量评分
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedQualityScores {
    /// 数据新鲜度评分 (0-100)
    pub freshness_score: f64,
    /// 流动性深度评分 (0-100)
    pub liquidity_score: f64,
    /// 价格稳定性评分 (0-100)
    pub stability_score: f64,
    /// 成交量评分 (0-100)
    pub volume_score: f64,
    /// 数据源可靠性评分 (0-100)
    pub source_reliability_score: f64,
    /// 价格准确性评分 (0-100)
    pub accuracy_score: f64,
    /// 一致性评分 (0-100)
    pub consistency_score: f64,
}

impl DetailedQualityScores {
    /// 计算加权总分
    pub fn calculate_weighted_score(&self, weights: &QualityWeights) -> f64 {
        self.freshness_score * weights.freshness_weight
            + self.liquidity_score * weights.liquidity_weight
            + self.stability_score * weights.stability_weight
            + self.volume_score * weights.volume_weight
            + self.source_reliability_score * weights.source_reliability_weight
    }
}

/// 质量标志
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum QualityFlag {
    /// 高质量数据
    HighQuality,
    /// 流动性低
    LowLiquidity,
    /// 价格波动大
    HighVolatility,
    /// 数据过时
    Stale,
    /// 异常价格
    AnomalousPrice,
    /// 可疑活动
    SuspiciousActivity,
    /// 数据缺失
    MissingData,
    /// 不一致的数据
    InconsistentData,
    /// 低置信度
    LowConfidence,
    /// 价格偏差大
    HighPriceDeviation,
    /// 成交量异常
    AbnormalVolume,
    /// 源不可靠
    UnreliableSource,
}

/// 风险级别
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum RiskLevel {
    /// 低风险
    Low,
    /// 中等风险
    Medium,
    /// 高风险
    High,
    /// 极高风险
    Critical,
}

impl From<f64> for RiskLevel {
    fn from(score: f64) -> Self {
        match score {
            s if s >= 80.0 => RiskLevel::Low,
            s if s >= 60.0 => RiskLevel::Medium,
            s if s >= 40.0 => RiskLevel::High,
            _ => RiskLevel::Critical,
        }
    }
}

/// 价格统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceStatistics {
    /// 平均价格
    pub mean: f64,
    /// 中位数价格
    pub median: f64,
    /// 标准差
    pub std_dev: f64,
    /// 最小值
    pub min: f64,
    /// 最大值
    pub max: f64,
    /// 偏度
    pub skewness: f64,
    /// 峰度
    pub kurtosis: f64,
    /// 样本数量
    pub count: usize,
    /// 变异系数
    pub coefficient_of_variation: f64,
}

impl PriceStatistics {
    /// 从价格序列计算统计信息
    pub fn from_prices(prices: &[f64]) -> Result<Self, QualityError> {
        if prices.is_empty() {
            return Err(QualityError::InsufficientData("价格序列为空".to_string()));
        }

        let count = prices.len();
        let sum: f64 = prices.iter().sum();
        let mean = sum / count as f64;

        // 计算方差
        let variance = prices.iter()
            .map(|x| (x - mean).powi(2))
            .sum::<f64>() / count as f64;
        let std_dev = variance.sqrt();

        // 计算中位数
        let mut sorted_prices = prices.to_vec();
        sorted_prices.sort_by(|a, b| a.partial_cmp(b).unwrap());
        let median = if count % 2 == 0 {
            (sorted_prices[count / 2 - 1] + sorted_prices[count / 2]) / 2.0
        } else {
            sorted_prices[count / 2]
        };

        let min = sorted_prices[0];
        let max = sorted_prices[count - 1];

        // 计算偏度和峰度
        let skewness = if std_dev > 0.0 {
            prices.iter()
                .map(|x| ((x - mean) / std_dev).powi(3))
                .sum::<f64>() / count as f64
        } else {
            0.0
        };

        let kurtosis = if std_dev > 0.0 {
            prices.iter()
                .map(|x| ((x - mean) / std_dev).powi(4))
                .sum::<f64>() / count as f64 - 3.0
        } else {
            0.0
        };

        let coefficient_of_variation = if mean != 0.0 {
            std_dev / mean.abs()
        } else {
            f64::INFINITY
        };

        Ok(Self {
            mean,
            median,
            std_dev,
            min,
            max,
            skewness,
            kurtosis,
            count,
            coefficient_of_variation,
        })
    }

    /// 计算Z分数
    pub fn z_score(&self, value: f64) -> f64 {
        if self.std_dev > 0.0 {
            (value - self.mean) / self.std_dev
        } else {
            0.0
        }
    }

    /// 检查是否为异常值
    pub fn is_outlier(&self, value: f64, threshold: f64) -> bool {
        self.z_score(value).abs() > threshold
    }
}

/// 质量监控事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityEvent {
    /// 事件ID
    pub event_id: String,
    /// 事件类型
    pub event_type: QualityEventType,
    /// 代币对
    pub token_pair: TokenPair,
    /// DEX类型
    pub dex_type: DexType,
    /// 事件描述
    pub description: String,
    /// 严重性级别
    pub severity: EventSeverity,
    /// 相关价格报价
    pub related_quote: Option<PriceQuote>,
    /// 事件数据
    pub event_data: HashMap<String, serde_json::Value>,
    /// 事件时间
    pub occurred_at: DateTime<Utc>,
    /// 处理状态
    pub status: EventStatus,
}

/// 质量事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QualityEventType {
    /// 价格异常
    PriceAnomaly,
    /// 质量下降
    QualityDegradation,
    /// 数据过时
    DataStale,
    /// 流动性不足
    InsufficientLiquidity,
    /// 价格偏差
    PriceDeviation,
    /// 成交量异常
    VolumeAnomaly,
    /// 源不可用
    SourceUnavailable,
    /// 验证失败
    ValidationFailure,
}

/// 事件严重性
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum EventSeverity {
    /// 信息
    Info,
    /// 警告
    Warning,
    /// 错误
    Error,
    /// 严重
    Critical,
}

/// 事件状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum EventStatus {
    /// 新建
    New,
    /// 正在处理
    Processing,
    /// 已解决
    Resolved,
    /// 已忽略
    Ignored,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_quality_config_default() {
        let config = QualityConfig::default();
        assert_eq!(config.min_freshness_seconds, 60);
        assert_eq!(config.max_price_deviation_percent, 10.0);
        assert!(!config.strict_mode);
    }

    #[test]
    fn test_quality_weights_sum() {
        let weights = QualityWeights::default();
        let sum = weights.freshness_weight
            + weights.liquidity_weight
            + weights.stability_weight
            + weights.volume_weight
            + weights.source_reliability_weight;
        
        // 权重总和应该是1.0
        assert!((sum - 1.0).abs() < 0.001);
    }

    #[test]
    fn test_risk_level_from_score() {
        assert_eq!(RiskLevel::from(90.0), RiskLevel::Low);
        assert_eq!(RiskLevel::from(70.0), RiskLevel::Medium);
        assert_eq!(RiskLevel::from(50.0), RiskLevel::High);
        assert_eq!(RiskLevel::from(30.0), RiskLevel::Critical);
    }

    #[test]
    fn test_price_statistics() {
        let prices = vec![100.0, 102.0, 98.0, 103.0, 97.0];
        let stats = PriceStatistics::from_prices(&prices).unwrap();
        
        assert_eq!(stats.count, 5);
        assert_eq!(stats.mean, 100.0);
        assert_eq!(stats.min, 97.0);
        assert_eq!(stats.max, 103.0);
        assert!(stats.std_dev > 0.0);
    }

    #[test]
    fn test_price_statistics_empty() {
        let prices = vec![];
        let result = PriceStatistics::from_prices(&prices);
        assert!(result.is_err());
    }

    #[test]
    fn test_z_score_calculation() {
        let prices = vec![100.0, 102.0, 98.0, 103.0, 97.0];
        let stats = PriceStatistics::from_prices(&prices).unwrap();
        
        let z_score = stats.z_score(110.0);
        assert!(z_score > 0.0); // 应该是正的异常值
        
        let normal_z = stats.z_score(100.0);
        assert!(normal_z.abs() < 0.1); // 平均值的Z分数应该接近0
    }

    #[test]
    fn test_outlier_detection() {
        let prices = vec![100.0, 102.0, 98.0, 103.0, 97.0];
        let stats = PriceStatistics::from_prices(&prices).unwrap();
        
        assert!(!stats.is_outlier(101.0, 2.0)); // 正常值
        assert!(stats.is_outlier(150.0, 2.0)); // 异常值
    }

    #[test]
    fn test_detailed_quality_scores() {
        let scores = DetailedQualityScores {
            freshness_score: 90.0,
            liquidity_score: 85.0,
            stability_score: 80.0,
            volume_score: 75.0,
            source_reliability_score: 95.0,
            accuracy_score: 88.0,
            consistency_score: 92.0,
        };
        
        let weights = QualityWeights::default();
        let weighted_score = scores.calculate_weighted_score(&weights);
        
        assert!(weighted_score > 0.0 && weighted_score <= 100.0);
    }
}