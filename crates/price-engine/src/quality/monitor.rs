//! Price Quality Monitor Implementation
//!
//! 价格质量监控器实现，负责监控和评估价格数据质量

use chrono::{DateTime, Utc};
use dashmap::DashMap;
use std::collections::{HashMap, VecDeque};
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{debug, info, warn, error};
use uuid::Uuid;

use super::{
    DetailedQualityScores, EventSeverity, EventStatus, PriceStatistics, 
    QualityAssessment, QualityConfig, QualityError, QualityEvent, 
    QualityEventType, QualityFlag, RiskLevel
};
use crate::types::{DexType, PriceQuote, TokenPair};

/// 价格历史记录
#[derive(Debug, Clone)]
struct PriceHistory {
    /// 价格序列
    prices: VecDeque<f64>,
    /// 时间戳序列
    timestamps: VecDeque<DateTime<Utc>>,
    /// 最大历史长度
    max_length: usize,
}

impl PriceHistory {
    fn new(max_length: usize) -> Self {
        Self {
            prices: VecDeque::with_capacity(max_length),
            timestamps: VecDeque::with_capacity(max_length),
            max_length,
        }
    }

    fn add_price(&mut self, price: f64, timestamp: DateTime<Utc>) {
        if self.prices.len() >= self.max_length {
            self.prices.pop_front();
            self.timestamps.pop_front();
        }
        self.prices.push_back(price);
        self.timestamps.push_back(timestamp);
    }

    fn get_prices(&self) -> Vec<f64> {
        self.prices.iter().cloned().collect()
    }

    fn get_recent_prices(&self, count: usize) -> Vec<f64> {
        let start = if self.prices.len() > count { 
            self.prices.len() - count 
        } else { 
            0 
        };
        self.prices.range(start..).cloned().collect()
    }

    fn is_empty(&self) -> bool {
        self.prices.is_empty()
    }

    fn len(&self) -> usize {
        self.prices.len()
    }

    fn latest_timestamp(&self) -> Option<DateTime<Utc>> {
        self.timestamps.back().cloned()
    }
}

/// DEX可靠性统计
#[derive(Debug, Clone)]
struct DexReliabilityStats {
    /// 总请求数
    total_requests: u64,
    /// 成功请求数
    successful_requests: u64,
    /// 质量评分历史
    quality_scores: VecDeque<f64>,
    /// 平均响应时间（毫秒）
    avg_response_time_ms: f64,
    /// 最后更新时间
    last_updated: DateTime<Utc>,
}

impl DexReliabilityStats {
    fn new() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            quality_scores: VecDeque::with_capacity(100),
            avg_response_time_ms: 0.0,
            last_updated: Utc::now(),
        }
    }

    fn record_request(&mut self, success: bool, response_time_ms: f64, quality_score: Option<f64>) {
        self.total_requests += 1;
        if success {
            self.successful_requests += 1;
        }

        // 更新平均响应时间
        self.avg_response_time_ms = (self.avg_response_time_ms * (self.total_requests - 1) as f64 
            + response_time_ms) / self.total_requests as f64;

        // 记录质量评分
        if let Some(score) = quality_score {
            if self.quality_scores.len() >= 100 {
                self.quality_scores.pop_front();
            }
            self.quality_scores.push_back(score);
        }

        self.last_updated = Utc::now();
    }

    fn success_rate(&self) -> f64 {
        if self.total_requests > 0 {
            self.successful_requests as f64 / self.total_requests as f64
        } else {
            0.0
        }
    }

    fn average_quality_score(&self) -> f64 {
        if self.quality_scores.is_empty() {
            0.0
        } else {
            self.quality_scores.iter().sum::<f64>() / self.quality_scores.len() as f64
        }
    }

    fn reliability_score(&self) -> f64 {
        let success_rate = self.success_rate() * 100.0;
        let quality_score = self.average_quality_score();
        let response_time_score = if self.avg_response_time_ms > 0.0 {
            (1000.0 / self.avg_response_time_ms).min(100.0)
        } else {
            100.0
        };

        (success_rate * 0.4 + quality_score * 0.4 + response_time_score * 0.2).min(100.0)
    }
}

/// 价格质量监控器
#[derive(Debug)]
pub struct PriceQualityMonitor {
    /// 配置
    config: QualityConfig,
    /// 价格历史 (token_pair, dex_type) -> PriceHistory
    price_histories: DashMap<(TokenPair, DexType), Arc<RwLock<PriceHistory>>>,
    /// DEX可靠性统计
    dex_stats: DashMap<DexType, Arc<RwLock<DexReliabilityStats>>>,
    /// 质量事件历史
    events: Arc<RwLock<VecDeque<QualityEvent>>>,
    /// 监控统计
    monitor_stats: Arc<RwLock<MonitorStats>>,
    /// 事件计数器
    event_counter: AtomicU64,
}

/// 监控统计信息
#[derive(Debug, Clone)]
struct MonitorStats {
    /// 总评估次数
    total_assessments: u64,
    /// 高质量评估次数
    high_quality_assessments: u64,
    /// 异常检测次数
    anomaly_detections: u64,
    /// 质量下降事件次数
    quality_degradation_events: u64,
    /// 平均质量评分
    average_quality_score: f64,
    /// 最后重置时间
    last_reset: DateTime<Utc>,
}

impl MonitorStats {
    fn new() -> Self {
        Self {
            total_assessments: 0,
            high_quality_assessments: 0,
            anomaly_detections: 0,
            quality_degradation_events: 0,
            average_quality_score: 0.0,
            last_reset: Utc::now(),
        }
    }

    fn high_quality_rate(&self) -> f64 {
        if self.total_assessments > 0 {
            self.high_quality_assessments as f64 / self.total_assessments as f64
        } else {
            0.0
        }
    }

    fn anomaly_rate(&self) -> f64 {
        if self.total_assessments > 0 {
            self.anomaly_detections as f64 / self.total_assessments as f64
        } else {
            0.0
        }
    }
}

impl PriceQualityMonitor {
    /// 创建新的价格质量监控器
    pub fn new() -> Self {
        Self::with_config(QualityConfig::default())
    }

    /// 使用配置创建价格质量监控器
    pub fn with_config(config: QualityConfig) -> Self {
        Self {
            config,
            price_histories: DashMap::new(),
            dex_stats: DashMap::new(),
            events: Arc::new(RwLock::new(VecDeque::with_capacity(1000))),
            monitor_stats: Arc::new(RwLock::new(MonitorStats::new())),
            event_counter: AtomicU64::new(0),
        }
    }

    /// 评估价格质量
    pub async fn assess_quality(&self, quote: &PriceQuote) -> Result<QualityAssessment, QualityError> {
        let start_time = std::time::Instant::now();
        
        // 记录价格历史
        self.record_price_history(quote).await;

        // 获取历史数据
        let key = (quote.token_pair.clone(), quote.source);
        let history = self.get_price_history(&key).await;

        // 计算详细评分
        let detailed_scores = self.calculate_detailed_scores(quote, &history).await?;
        
        // 计算总体评分
        let overall_score = detailed_scores.calculate_weighted_score(&self.config.quality_weights);

        // 生成质量标志
        let quality_flags = self.generate_quality_flags(quote, &detailed_scores, &history).await;

        // 确定风险级别
        let risk_level = RiskLevel::from(overall_score);

        // 生成建议
        let recommendations = self.generate_recommendations(&quality_flags, &detailed_scores);

        // 计算置信度
        let confidence = self.calculate_confidence(&history, &detailed_scores);

        let assessment = QualityAssessment {
            token_pair: quote.token_pair.clone(),
            dex_type: quote.source,
            overall_score,
            detailed_scores,
            quality_flags: quality_flags.clone(),
            risk_level: risk_level.clone(),
            recommendations,
            assessed_at: Utc::now(),
            sample_count: history.len(),
            confidence,
        };

        // 更新统计信息
        self.update_monitor_stats(&assessment).await;

        // 记录DEX统计
        self.update_dex_stats(quote.source, true, start_time.elapsed().as_millis() as f64, Some(overall_score)).await;

        // 检查是否需要生成事件
        self.check_and_generate_events(quote, &assessment).await?;

        debug!(
            "质量评估完成: {} {} 评分: {:.1} 风险: {:?}",
            quote.token_pair, quote.source, overall_score, risk_level
        );

        Ok(assessment)
    }

    /// 记录价格历史
    async fn record_price_history(&self, quote: &PriceQuote) {
        let key = (quote.token_pair.clone(), quote.source);
        
        let history = self.price_histories
            .entry(key)
            .or_insert_with(|| Arc::new(RwLock::new(
                PriceHistory::new(self.config.stability_window_size * 2)
            )))
            .clone();

        let mut history_guard = history.write().await;
        history_guard.add_price(quote.spot_price, quote.timestamp);
    }

    /// 获取价格历史
    async fn get_price_history(&self, key: &(TokenPair, DexType)) -> Vec<f64> {
        if let Some(history_arc) = self.price_histories.get(key) {
            let history = history_arc.read().await;
            history.get_prices()
        } else {
            Vec::new()
        }
    }

    /// 计算详细评分
    async fn calculate_detailed_scores(
        &self, 
        quote: &PriceQuote, 
        history: &[f64]
    ) -> Result<DetailedQualityScores, QualityError> {
        // 1. 数据新鲜度评分
        let freshness_score = self.calculate_freshness_score(quote);

        // 2. 流动性评分
        let liquidity_score = self.calculate_liquidity_score(quote);

        // 3. 价格稳定性评分
        let stability_score = self.calculate_stability_score(history);

        // 4. 成交量评分
        let volume_score = self.calculate_volume_score(quote);

        // 5. 数据源可靠性评分
        let source_reliability_score = self.calculate_source_reliability_score(quote.source).await;

        // 6. 价格准确性评分
        let accuracy_score = self.calculate_accuracy_score(quote, history);

        // 7. 一致性评分
        let consistency_score = self.calculate_consistency_score(quote, history);

        Ok(DetailedQualityScores {
            freshness_score,
            liquidity_score,
            stability_score,
            volume_score,
            source_reliability_score,
            accuracy_score,
            consistency_score,
        })
    }

    /// 计算新鲜度评分
    fn calculate_freshness_score(&self, quote: &PriceQuote) -> f64 {
        let age_seconds = (Utc::now() - quote.timestamp).num_seconds() as u64;
        
        if age_seconds <= self.config.min_freshness_seconds {
            100.0
        } else {
            let max_age = self.config.min_freshness_seconds * 5; // 5倍阈值为0分
            let score = 100.0 * (1.0 - (age_seconds as f64 / max_age as f64));
            score.max(0.0)
        }
    }

    /// 计算流动性评分
    fn calculate_liquidity_score(&self, quote: &PriceQuote) -> f64 {
        // 基于TVL计算流动性评分
        match quote.tvl {
            Some(tvl) => {
                if tvl >= self.config.min_liquidity_threshold as u64 {
                    let normalized = (tvl as f64).log10() / 9.0; // 假设最大TVL为10^9
                    (normalized * 100.0).min(100.0)
                } else {
                    let ratio = tvl as f64 / self.config.min_liquidity_threshold as f64;
                    (ratio * 50.0).min(50.0) // 低于阈值最高50分
                }
            }
            None => 70.0, // 没有TVL数据给默认分数
        }
    }

    /// 计算稳定性评分
    fn calculate_stability_score(&self, history: &[f64]) -> f64 {
        if history.len() < 2 {
            return 50.0; // 数据不足，给中等分数
        }

        match PriceStatistics::from_prices(history) {
            Ok(stats) => {
                // 使用变异系数计算稳定性
                let cv = stats.coefficient_of_variation;
                let max_acceptable_cv = self.config.max_volatility_percent / 100.0;
                
                if cv <= max_acceptable_cv {
                    100.0
                } else {
                    let score = 100.0 * (1.0 - (cv - max_acceptable_cv) / max_acceptable_cv);
                    score.max(0.0)
                }
            }
            Err(_) => 0.0,
        }
    }

    /// 计算成交量评分
    fn calculate_volume_score(&self, quote: &PriceQuote) -> f64 {
        match quote.volume_24h {
            Some(volume) => {
                // 基于24小时成交量的对数计算评分
                if volume > 0 {
                    let log_volume = (volume as f64).log10();
                    let normalized = log_volume / 8.0; // 假设最大成交量为10^8
                    (normalized * 100.0).min(100.0).max(0.0)
                } else {
                    0.0
                }
            }
            None => 60.0, // 没有成交量数据给默认分数
        }
    }

    /// 计算数据源可靠性评分
    async fn calculate_source_reliability_score(&self, dex_type: DexType) -> f64 {
        if let Some(stats_arc) = self.dex_stats.get(&dex_type) {
            let stats = stats_arc.read().await;
            stats.reliability_score()
        } else {
            80.0 // 新数据源给默认高分
        }
    }

    /// 计算准确性评分
    fn calculate_accuracy_score(&self, quote: &PriceQuote, history: &[f64]) -> f64 {
        if history.len() < 3 {
            return 85.0; // 数据不足，给较高默认分数
        }

        match PriceStatistics::from_prices(history) {
            Ok(stats) => {
                let z_score = stats.z_score(quote.spot_price);
                if z_score.abs() <= 2.0 {
                    100.0 - z_score.abs() * 10.0 // Z分数越小，准确性越高
                } else {
                    50.0 - ((z_score.abs() - 2.0) * 10.0).min(50.0) // 异常值惩罚
                }
            }
            Err(_) => 0.0,
        }
    }

    /// 计算一致性评分
    fn calculate_consistency_score(&self, quote: &PriceQuote, history: &[f64]) -> f64 {
        if history.len() < 2 {
            return 90.0;
        }

        // 检查置信区间的合理性
        let ci = &quote.confidence_interval;
        let spread = (ci.upper_bound - ci.lower_bound) / quote.spot_price;
        
        // 价格应该在置信区间内
        let price_in_ci = quote.spot_price >= ci.lower_bound && quote.spot_price <= ci.upper_bound;
        
        let mut score: f64 = if price_in_ci { 90.0 } else { 50.0 };
        
        // 置信区间宽度合理性
        if spread <= 0.1 { // 10%以内认为合理
            score += 10.0;
        } else if spread > 0.2 { // 超过20%扣分
            score -= 20.0;
        }

        score.max(0.0).min(100.0)
    }

    /// 生成质量标志
    async fn generate_quality_flags(
        &self, 
        quote: &PriceQuote, 
        scores: &DetailedQualityScores,
        history: &[f64]
    ) -> Vec<QualityFlag> {
        let mut flags = Vec::new();

        // 高质量数据
        if scores.calculate_weighted_score(&self.config.quality_weights) >= 90.0 {
            flags.push(QualityFlag::HighQuality);
        }

        // 数据过时
        if scores.freshness_score < 70.0 {
            flags.push(QualityFlag::Stale);
        }

        // 流动性低
        if scores.liquidity_score < 60.0 {
            flags.push(QualityFlag::LowLiquidity);
        }

        // 价格波动大
        if scores.stability_score < 50.0 {
            flags.push(QualityFlag::HighVolatility);
        }

        // 异常价格
        if scores.accuracy_score < 50.0 {
            flags.push(QualityFlag::AnomalousPrice);
        }

        // 低置信度
        if quote.confidence_interval.confidence_level < self.config.required_confidence_level {
            flags.push(QualityFlag::LowConfidence);
        }

        // 数据缺失
        if quote.tvl.is_none() || quote.volume_24h.is_none() {
            flags.push(QualityFlag::MissingData);
        }

        // 不一致数据
        if scores.consistency_score < 70.0 {
            flags.push(QualityFlag::InconsistentData);
        }

        // 源不可靠
        if scores.source_reliability_score < 60.0 {
            flags.push(QualityFlag::UnreliableSource);
        }

        // 价格偏差大
        if history.len() >= 3 {
            if let Ok(stats) = PriceStatistics::from_prices(history) {
                if stats.is_outlier(quote.spot_price, self.config.anomaly_sensitivity) {
                    flags.push(QualityFlag::HighPriceDeviation);
                }
            }
        }

        flags
    }

    /// 生成建议
    fn generate_recommendations(
        &self, 
        flags: &[QualityFlag], 
        scores: &DetailedQualityScores
    ) -> Vec<String> {
        let mut recommendations = Vec::new();

        for flag in flags {
            match flag {
                QualityFlag::Stale => {
                    recommendations.push("建议使用更新的数据源".to_string());
                }
                QualityFlag::LowLiquidity => {
                    recommendations.push("建议谨慎处理大额交易".to_string());
                }
                QualityFlag::HighVolatility => {
                    recommendations.push("建议增加价格监控频率".to_string());
                }
                QualityFlag::AnomalousPrice => {
                    recommendations.push("建议验证价格准确性".to_string());
                }
                QualityFlag::LowConfidence => {
                    recommendations.push("建议获取更多数据点以提高置信度".to_string());
                }
                QualityFlag::MissingData => {
                    recommendations.push("建议补充缺失的市场数据".to_string());
                }
                QualityFlag::UnreliableSource => {
                    recommendations.push("建议使用备用数据源".to_string());
                }
                QualityFlag::HighPriceDeviation => {
                    recommendations.push("建议交叉验证价格数据".to_string());
                }
                _ => {}
            }
        }

        // 基于评分的通用建议
        if scores.calculate_weighted_score(&self.config.quality_weights) < 60.0 {
            recommendations.push("建议暂停自动交易并人工审核".to_string());
        } else if scores.calculate_weighted_score(&self.config.quality_weights) < 80.0 {
            recommendations.push("建议增加风险控制措施".to_string());
        }

        recommendations
    }

    /// 计算置信度
    fn calculate_confidence(&self, history: &[f64], scores: &DetailedQualityScores) -> f64 {
        let mut confidence = 0.0;

        // 基于样本数量
        let sample_confidence = if history.len() >= self.config.min_sample_count {
            (history.len() as f64 / (self.config.min_sample_count * 2) as f64).min(1.0) * 30.0
        } else {
            (history.len() as f64 / self.config.min_sample_count as f64) * 15.0
        };

        confidence += sample_confidence;

        // 基于各项评分
        confidence += scores.freshness_score * 0.15;
        confidence += scores.liquidity_score * 0.15;
        confidence += scores.stability_score * 0.15;
        confidence += scores.accuracy_score * 0.15;
        confidence += scores.consistency_score * 0.10;

        confidence.min(100.0)
    }

    /// 更新监控统计
    async fn update_monitor_stats(&self, assessment: &QualityAssessment) {
        let mut stats = self.monitor_stats.write().await;
        
        stats.total_assessments += 1;
        
        if assessment.overall_score >= 80.0 {
            stats.high_quality_assessments += 1;
        }

        if assessment.quality_flags.contains(&QualityFlag::AnomalousPrice) ||
           assessment.quality_flags.contains(&QualityFlag::HighPriceDeviation) {
            stats.anomaly_detections += 1;
        }

        if assessment.overall_score < 60.0 {
            stats.quality_degradation_events += 1;
        }

        // 更新平均质量评分
        stats.average_quality_score = (stats.average_quality_score * (stats.total_assessments - 1) as f64 
            + assessment.overall_score) / stats.total_assessments as f64;
    }

    /// 更新DEX统计
    async fn update_dex_stats(
        &self, 
        dex_type: DexType, 
        success: bool, 
        response_time_ms: f64,
        quality_score: Option<f64>
    ) {
        let stats = self.dex_stats
            .entry(dex_type)
            .or_insert_with(|| Arc::new(RwLock::new(DexReliabilityStats::new())))
            .clone();

        let mut stats_guard = stats.write().await;
        stats_guard.record_request(success, response_time_ms, quality_score);
    }

    /// 检查并生成质量事件
    async fn check_and_generate_events(
        &self, 
        quote: &PriceQuote, 
        assessment: &QualityAssessment
    ) -> Result<(), QualityError> {
        let mut should_generate_event = false;
        let mut event_type = QualityEventType::PriceAnomaly;
        let mut severity = EventSeverity::Info;
        let mut description = String::new();

        // 检查各种质量问题
        if assessment.overall_score < 40.0 {
            should_generate_event = true;
            event_type = QualityEventType::QualityDegradation;
            severity = EventSeverity::Critical;
            description = format!("质量评分严重下降: {:.1}", assessment.overall_score);
        } else if assessment.quality_flags.contains(&QualityFlag::AnomalousPrice) {
            should_generate_event = true;
            event_type = QualityEventType::PriceAnomaly;
            severity = EventSeverity::Warning;
            description = "检测到异常价格".to_string();
        } else if assessment.quality_flags.contains(&QualityFlag::Stale) {
            should_generate_event = true;
            event_type = QualityEventType::DataStale;
            severity = EventSeverity::Warning;
            description = "数据过时".to_string();
        } else if assessment.quality_flags.contains(&QualityFlag::LowLiquidity) {
            should_generate_event = true;
            event_type = QualityEventType::InsufficientLiquidity;
            severity = EventSeverity::Info;
            description = "流动性不足".to_string();
        }

        if should_generate_event {
            let event = QualityEvent {
                event_id: format!("qe_{}", self.event_counter.fetch_add(1, Ordering::Relaxed)),
                event_type,
                token_pair: quote.token_pair.clone(),
                dex_type: quote.source,
                description,
                severity: severity.clone(),
                related_quote: Some(quote.clone()),
                event_data: {
                    let mut data = HashMap::new();
                    data.insert("quality_score".to_string(), 
                        serde_json::json!(assessment.overall_score));
                    data.insert("risk_level".to_string(), 
                        serde_json::json!(assessment.risk_level));
                    data.insert("flags".to_string(), 
                        serde_json::json!(assessment.quality_flags));
                    data
                },
                occurred_at: Utc::now(),
                status: EventStatus::New,
            };

            // 添加到事件历史
            let mut events = self.events.write().await;
            if events.len() >= 1000 {
                events.pop_front();
            }
            events.push_back(event.clone());

            // 记录日志
            match severity {
                EventSeverity::Critical => error!("质量事件: {}", event.description),
                EventSeverity::Error => error!("质量事件: {}", event.description),
                EventSeverity::Warning => warn!("质量事件: {}", event.description),
                EventSeverity::Info => info!("质量事件: {}", event.description),
            }
        }

        Ok(())
    }

    /// 获取DEX可靠性统计
    pub async fn get_dex_reliability(&self, dex_type: DexType) -> Option<f64> {
        if let Some(stats_arc) = self.dex_stats.get(&dex_type) {
            let stats = stats_arc.read().await;
            Some(stats.reliability_score())
        } else {
            None
        }
    }

    /// 获取质量事件
    pub async fn get_quality_events(&self, limit: Option<usize>) -> Vec<QualityEvent> {
        let events = self.events.read().await;
        let limit = limit.unwrap_or(100);
        
        events.iter()
            .rev()
            .take(limit)
            .cloned()
            .collect()
    }

    /// 获取监控统计
    pub async fn get_monitor_stats(&self) -> MonitorStats {
        self.monitor_stats.read().await.clone()
    }

    /// 清理历史数据
    pub async fn cleanup_history(&self, retention_hours: u64) {
        let cutoff_time = Utc::now() - chrono::Duration::hours(retention_hours as i64);
        
        // 清理价格历史
        for entry in self.price_histories.iter() {
            let history_arc = entry.value();
            let mut history = history_arc.write().await;
            
            // 移除过期的价格数据
            while let Some(&timestamp) = history.timestamps.front() {
                if timestamp < cutoff_time {
                    history.timestamps.pop_front();
                    history.prices.pop_front();
                } else {
                    break;
                }
            }
        }

        // 清理质量事件
        let mut events = self.events.write().await;
        events.retain(|event| event.occurred_at > cutoff_time);

        debug!("清理了 {} 小时前的历史数据", retention_hours);
    }

    /// 重置监控统计
    pub async fn reset_stats(&self) {
        let mut stats = self.monitor_stats.write().await;
        *stats = MonitorStats::new();
        
        // 重置DEX统计
        for entry in self.dex_stats.iter() {
            let stats_arc = entry.value();
            let mut dex_stats = stats_arc.write().await;
            *dex_stats = DexReliabilityStats::new();
        }

        info!("已重置监控统计数据");
    }

    /// 更新配置
    pub fn update_config(&mut self, config: QualityConfig) {
        self.config = config;
        info!("已更新质量监控配置");
    }

    /// 获取当前配置
    pub fn get_config(&self) -> &QualityConfig {
        &self.config
    }
}

impl Default for PriceQualityMonitor {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{TokenPair, ConfidenceInterval};
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    fn create_test_quote(price: f64, timestamp: DateTime<Utc>) -> PriceQuote {
        PriceQuote {
            token_pair: TokenPair::new(
                Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
                Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            ),
            spot_price: price,
            bid_price: price * 0.999,
            ask_price: price * 1.001,
            spread_percent: 0.2,
            price_change_24h: Some(5.5),
            volume_24h: Some(1000000),
            tvl: Some(50000000),
            source: DexType::RaydiumClmm,
            pool_id: Pubkey::default(),
            quality_score: 95.0,
            confidence_interval: ConfidenceInterval {
                confidence_level: 0.95,
                lower_bound: price * 0.95,
                upper_bound: price * 1.05,
            },
            timestamp,
            expires_at: timestamp + chrono::Duration::seconds(60),
        }
    }

    #[tokio::test]
    async fn test_quality_monitor_creation() {
        let monitor = PriceQualityMonitor::new();
        assert!(monitor.price_histories.is_empty());
        assert!(monitor.dex_stats.is_empty());
    }

    #[tokio::test]
    async fn test_price_history_recording() {
        let monitor = PriceQualityMonitor::new();
        let quote = create_test_quote(100.0, Utc::now());
        
        monitor.record_price_history(&quote).await;
        
        let key = (quote.token_pair.clone(), quote.source);
        let history = monitor.get_price_history(&key).await;
        assert_eq!(history.len(), 1);
        assert_eq!(history[0], 100.0);
    }

    #[tokio::test]
    async fn test_quality_assessment() {
        let monitor = PriceQualityMonitor::new();
        let quote = create_test_quote(100.0, Utc::now());
        
        let assessment = monitor.assess_quality(&quote).await.unwrap();
        
        assert_eq!(assessment.token_pair, quote.token_pair);
        assert_eq!(assessment.dex_type, quote.source);
        assert!(assessment.overall_score >= 0.0 && assessment.overall_score <= 100.0);
        assert!(assessment.confidence >= 0.0);
    }

    #[tokio::test]
    async fn test_freshness_score_calculation() {
        let monitor = PriceQualityMonitor::new();
        
        // 新鲜数据
        let fresh_quote = create_test_quote(100.0, Utc::now());
        let fresh_score = monitor.calculate_freshness_score(&fresh_quote);
        assert_eq!(fresh_score, 100.0);
        
        // 过时数据
        let stale_quote = create_test_quote(100.0, Utc::now() - chrono::Duration::minutes(10));
        let stale_score = monitor.calculate_freshness_score(&stale_quote);
        assert!(stale_score < 100.0);
    }

    #[tokio::test]
    async fn test_stability_score_calculation() {
        let monitor = PriceQualityMonitor::new();
        
        // 稳定价格序列
        let stable_prices = vec![100.0, 100.1, 99.9, 100.2, 99.8];
        let stable_score = monitor.calculate_stability_score(&stable_prices);
        
        // 不稳定价格序列
        let volatile_prices = vec![100.0, 150.0, 80.0, 120.0, 90.0];
        let volatile_score = monitor.calculate_stability_score(&volatile_prices);
        
        assert!(stable_score > volatile_score);
    }

    #[tokio::test]
    async fn test_quality_flags_generation() {
        let monitor = PriceQualityMonitor::new();
        
        let scores = DetailedQualityScores {
            freshness_score: 95.0,
            liquidity_score: 95.0,
            stability_score: 95.0,
            volume_score: 95.0,
            source_reliability_score: 95.0,
            accuracy_score: 95.0,
            consistency_score: 95.0,
        };
        
        let quote = create_test_quote(100.0, Utc::now());
        let history = vec![100.0, 99.5, 100.5, 99.8, 100.2];
        
        let flags = monitor.generate_quality_flags(&quote, &scores, &history).await;
        assert!(flags.contains(&QualityFlag::HighQuality));
    }

    #[tokio::test]
    async fn test_dex_reliability_tracking() {
        let monitor = PriceQualityMonitor::new();
        
        // 记录一些成功的请求
        monitor.update_dex_stats(DexType::RaydiumClmm, true, 50.0, Some(90.0)).await;
        monitor.update_dex_stats(DexType::RaydiumClmm, true, 60.0, Some(85.0)).await;
        monitor.update_dex_stats(DexType::RaydiumClmm, false, 100.0, None).await;
        
        let reliability = monitor.get_dex_reliability(DexType::RaydiumClmm).await;
        assert!(reliability.is_some());
        assert!(reliability.unwrap() > 0.0);
    }

    #[tokio::test]
    async fn test_event_generation() {
        let monitor = PriceQualityMonitor::new();
        
        // 创建一个低质量的报价
        let mut bad_quote = create_test_quote(100.0, Utc::now() - chrono::Duration::hours(2));
        bad_quote.tvl = Some(1000); // 低流动性
        
        let assessment = monitor.assess_quality(&bad_quote).await.unwrap();
        
        let events = monitor.get_quality_events(Some(10)).await;
        // 应该生成至少一个事件
        assert!(!events.is_empty());
    }

    #[tokio::test]
    async fn test_monitor_stats() {
        let monitor = PriceQualityMonitor::new();
        let quote = create_test_quote(100.0, Utc::now());
        
        // 执行几次评估
        for _ in 0..5 {
            let _ = monitor.assess_quality(&quote).await;
        }
        
        let stats = monitor.get_monitor_stats().await;
        assert_eq!(stats.total_assessments, 5);
        assert!(stats.average_quality_score > 0.0);
    }

    #[test]
    fn test_price_history() {
        let mut history = PriceHistory::new(3);
        
        history.add_price(100.0, Utc::now());
        history.add_price(101.0, Utc::now());
        history.add_price(102.0, Utc::now());
        
        assert_eq!(history.len(), 3);
        
        // 添加第四个价格，应该移除第一个
        history.add_price(103.0, Utc::now());
        
        assert_eq!(history.len(), 3);
        let prices = history.get_prices();
        assert_eq!(prices[0], 101.0); // 第一个价格应该被移除
        assert_eq!(prices[2], 103.0); // 最新价格
    }

    #[test]
    fn test_dex_reliability_stats() {
        let mut stats = DexReliabilityStats::new();
        
        stats.record_request(true, 50.0, Some(90.0));
        stats.record_request(true, 60.0, Some(85.0));
        stats.record_request(false, 100.0, None);
        
        assert_eq!(stats.total_requests, 3);
        assert_eq!(stats.successful_requests, 2);
        assert_eq!(stats.success_rate(), 2.0 / 3.0);
        assert_eq!(stats.average_quality_score(), 87.5);
        assert!(stats.reliability_score() > 0.0);
    }
}