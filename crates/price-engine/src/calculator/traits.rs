//! Price Calculator Traits
//!
//! 定义价格计算器的核心 trait 接口

use async_trait::async_trait;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;

use crate::types::*;

/// 价格计算器核心接口
/// 
/// 所有DEX特定的价格计算器都需要实现这个trait
#[async_trait]
pub trait PriceCalculator: Send + Sync + std::fmt::Debug {
    /// 计算现货价格
    /// 
    /// # 参数
    /// * `input_token` - 输入代币地址
    /// * `output_token` - 输出代币地址
    /// 
    /// # 返回
    /// 返回当前现货价格（output_token/input_token）
    async fn calculate_spot_price(
        &self,
        input_token: &Pubkey,
        output_token: &Pubkey,
    ) -> PriceEngineResult<f64>;

    /// 估算交换输出
    /// 
    /// # 参数
    /// * `input_amount` - 输入金额
    /// * `input_token` - 输入代币地址
    /// * `output_token` - 输出代币地址
    /// 
    /// # 返回
    /// 返回详细的交换估算结果
    async fn estimate_swap_output(
        &self,
        input_amount: u64,
        input_token: &Pubkey,
        output_token: &Pubkey,
    ) -> PriceEngineResult<SwapEstimation>;

    /// 计算有效价格（考虑交换量的影响）
    /// 
    /// # 参数
    /// * `input_amount` - 输入金额
    /// * `input_token` - 输入代币地址
    /// * `output_token` - 输出代币地址
    /// 
    /// # 返回
    /// 返回有效交换价格
    async fn get_effective_price(
        &self,
        input_amount: u64,
        input_token: &Pubkey,
        output_token: &Pubkey,
    ) -> PriceEngineResult<f64>;

    /// 获取流动性深度信息
    /// 
    /// # 参数
    /// * `token_pair` - 代币对
    /// * `price_range_percent` - 价格范围百分比（例如10.0表示±10%）
    /// 
    /// # 返回
    /// 返回指定价格范围内的流动性分布
    async fn get_liquidity_depth(
        &self,
        token_pair: &TokenPair,
        price_range_percent: f64,
    ) -> PriceEngineResult<LiquidityDepth>;

    /// 计算价格影响
    /// 
    /// # 参数
    /// * `input_amount` - 输入金额
    /// * `input_token` - 输入代币地址
    /// * `output_token` - 输出代币地址
    /// 
    /// # 返回
    /// 返回价格影响百分比
    async fn calculate_price_impact(
        &self,
        input_amount: u64,
        input_token: &Pubkey,
        output_token: &Pubkey,
    ) -> PriceEngineResult<f64>;

    /// 获取支持的代币对列表
    /// 
    /// # 返回
    /// 返回所有支持的代币对
    async fn get_supported_pairs(&self) -> PriceEngineResult<Vec<TokenPair>>;

    /// 获取计算器类型
    fn get_dex_type(&self) -> DexType;

    /// 获取计算器名称
    fn get_name(&self) -> &'static str;

    /// 检查是否支持指定的代币对
    /// 
    /// # 参数
    /// * `token_pair` - 代币对
    /// 
    /// # 返回
    /// 如果支持返回true，否则返回false
    async fn supports_pair(&self, token_pair: &TokenPair) -> bool;

    /// 获取池的基本信息
    /// 
    /// # 参数
    /// * `token_pair` - 代币对
    /// 
    /// # 返回
    /// 返回池信息，如果池不存在则返回None
    async fn get_pool_info(&self, token_pair: &TokenPair) -> PriceEngineResult<Option<PoolInfo>>;

    /// 计算反向交换（给定输出金额，计算所需输入）
    /// 
    /// # 参数
    /// * `output_amount` - 期望的输出金额
    /// * `input_token` - 输入代币地址
    /// * `output_token` - 输出代币地址
    /// 
    /// # 返回
    /// 返回所需的输入金额估算
    async fn estimate_input_for_output(
        &self,
        output_amount: u64,
        input_token: &Pubkey,
        output_token: &Pubkey,
    ) -> PriceEngineResult<SwapEstimation>;

    /// 批量计算多个代币对的现货价格
    /// 
    /// # 参数
    /// * `token_pairs` - 代币对列表
    /// 
    /// # 返回
    /// 返回所有代币对的价格映射
    async fn batch_spot_prices(
        &self,
        token_pairs: &[TokenPair],
    ) -> PriceEngineResult<HashMap<TokenPair, f64>>;

    /// 检查计算器状态是否健康
    /// 
    /// # 返回
    /// 如果状态正常返回Ok，否则返回错误信息
    async fn health_check(&self) -> PriceEngineResult<HealthStatus>;

    /// 获取性能统计信息
    /// 
    /// # 返回
    /// 返回性能统计数据
    fn get_performance_stats(&self) -> PerformanceStats;

    /// 重置性能统计
    fn reset_performance_stats(&self);
}

/// 池信息结构
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PoolInfo {
    /// 池ID
    pub pool_id: Pubkey,
    /// 代币对
    pub token_pair: TokenPair,
    /// 总流动性
    pub total_liquidity: u128,
    /// 当前价格
    pub current_price: f64,
    /// 费用率（基点）
    pub fee_rate: u32,
    /// 最后更新时间
    pub last_updated: chrono::DateTime<chrono::Utc>,
    /// 是否活跃
    pub is_active: bool,
    /// TVL（总锁定价值）
    pub tvl_usd: Option<f64>,
    /// 24小时交易量
    pub volume_24h_usd: Option<f64>,
}

/// 健康状态
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct HealthStatus {
    /// 是否健康
    pub is_healthy: bool,
    /// 状态消息
    pub message: String,
    /// 最后检查时间
    pub last_check: chrono::DateTime<chrono::Utc>,
    /// 延迟（毫秒）
    pub latency_ms: u64,
    /// 错误计数（最近1分钟）
    pub error_count: u32,
    /// 成功率（最近1分钟）
    pub success_rate: f64,
}

/// 价格聚合器接口
/// 
/// 用于聚合多个DEX的价格信息
#[async_trait]
pub trait PriceAggregator: Send + Sync {
    /// 获取最佳价格
    /// 
    /// # 参数
    /// * `token_pair` - 代币对
    /// * `input_amount` - 输入金额
    /// 
    /// # 返回
    /// 返回来自所有DEX的最佳价格
    async fn get_best_price(
        &self,
        token_pair: &TokenPair,
        input_amount: u64,
    ) -> PriceEngineResult<AggregatedPrice>;

    /// 获取所有DEX的价格
    /// 
    /// # 参数
    /// * `token_pair` - 代币对
    /// * `input_amount` - 输入金额可选
    /// 
    /// # 返回
    /// 返回所有DEX的价格列表
    async fn get_all_prices(
        &self,
        token_pair: &TokenPair,
        input_amount: Option<u64>,
    ) -> PriceEngineResult<Vec<PriceQuote>>;

    /// 获取价格差异分析
    /// 
    /// # 参数
    /// * `token_pair` - 代币对
    /// 
    /// # 返回
    /// 返回各DEX间的价格差异分析
    async fn get_price_spread_analysis(
        &self,
        token_pair: &TokenPair,
    ) -> PriceEngineResult<PriceSpreadAnalysis>;
}

/// 聚合价格结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AggregatedPrice {
    /// 最佳价格报价
    pub best_quote: PriceQuote,
    /// 所有报价
    pub all_quotes: Vec<PriceQuote>,
    /// 聚合统计
    pub aggregation_stats: AggregationStats,
}

/// 聚合统计
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AggregationStats {
    /// 参与的DEX数量
    pub dex_count: usize,
    /// 最高价格
    pub highest_price: f64,
    /// 最低价格
    pub lowest_price: f64,
    /// 平均价格
    pub average_price: f64,
    /// 加权平均价格（按流动性加权）
    pub weighted_average_price: f64,
    /// 价格标准差
    pub price_std_dev: f64,
    /// 聚合时间戳
    pub aggregated_at: chrono::DateTime<chrono::Utc>,
}

/// 价格差异分析
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct PriceSpreadAnalysis {
    /// 代币对
    pub token_pair: TokenPair,
    /// 最大价差百分比
    pub max_spread_percent: f64,
    /// 平均价差百分比
    pub average_spread_percent: f64,
    /// 套利机会
    pub arbitrage_opportunities: Vec<ArbitrageOpportunity>,
    /// 分析时间戳
    pub analyzed_at: chrono::DateTime<chrono::Utc>,
}

/// 套利机会
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ArbitrageOpportunity {
    /// 买入DEX
    pub buy_dex: DexType,
    /// 卖出DEX
    pub sell_dex: DexType,
    /// 价格差异百分比
    pub price_difference_percent: f64,
    /// 潜在利润（考虑费用）
    pub potential_profit_percent: f64,
    /// 推荐交换金额
    pub recommended_amount: u64,
    /// 机会评分 (0-100)
    pub opportunity_score: f64,
}

/// 实时价格监听器接口
#[async_trait]
pub trait PriceListener: Send + Sync {
    /// 启动价格监听
    async fn start(&self) -> PriceEngineResult<()>;

    /// 停止价格监听
    async fn stop(&self) -> PriceEngineResult<()>;

    /// 添加代币对监听
    /// 
    /// # 参数
    /// * `token_pair` - 要监听的代币对
    async fn add_pair(&self, token_pair: TokenPair) -> PriceEngineResult<()>;

    /// 移除代币对监听
    /// 
    /// # 参数
    /// * `token_pair` - 要移除的代币对
    async fn remove_pair(&self, token_pair: &TokenPair) -> PriceEngineResult<()>;

    /// 获取监听状态
    async fn get_status(&self) -> PriceEngineResult<ListenerStatus>;
}

/// 监听器状态
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ListenerStatus {
    /// 是否运行中
    pub is_running: bool,
    /// 监听的代币对数量
    pub monitored_pairs: usize,
    /// 最后更新时间
    pub last_update: chrono::DateTime<chrono::Utc>,
    /// 更新频率（次/秒）
    pub update_frequency: f64,
    /// 错误计数
    pub error_count: u64,
}

/// 价格验证器接口
pub trait PriceValidator: Send + Sync {
    /// 验证价格合理性
    /// 
    /// # 参数
    /// * `quote` - 价格报价
    /// * `historical_data` - 历史价格数据
    /// 
    /// # 返回
    /// 返回验证结果
    fn validate_price(
        &self,
        quote: &PriceQuote,
        historical_data: Option<&[f64]>,
    ) -> PriceEngineResult<ValidationResult>;

    /// 检测价格异常
    /// 
    /// # 参数
    /// * `current_price` - 当前价格
    /// * `historical_prices` - 历史价格序列
    /// 
    /// # 返回
    /// 返回异常检测结果
    fn detect_anomaly(
        &self,
        current_price: f64,
        historical_prices: &[f64],
    ) -> PriceEngineResult<AnomalyResult>;
}

/// 验证结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ValidationResult {
    /// 是否有效
    pub is_valid: bool,
    /// 置信度 (0-100)
    pub confidence: f64,
    /// 验证消息
    pub messages: Vec<String>,
    /// 质量评分 (0-100)
    pub quality_score: f64,
}

/// 异常检测结果
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct AnomalyResult {
    /// 是否为异常
    pub is_anomaly: bool,
    /// 异常评分 (0-100，分数越高越异常)
    pub anomaly_score: f64,
    /// 异常类型
    pub anomaly_type: Option<AnomalyType>,
    /// 置信区间
    pub confidence_interval: (f64, f64),
}

/// 异常类型
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub enum AnomalyType {
    /// 价格突变
    PriceSpike,
    /// 价格暴跌
    PriceCrash,
    /// 异常波动
    UnusualVolatility,
    /// 数据缺失
    MissingData,
    /// 系统错误
    SystemError,
}