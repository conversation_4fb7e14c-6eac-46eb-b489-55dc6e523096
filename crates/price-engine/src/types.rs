//! Price Engine 核心数据类型定义
//!
//! 定义价格计算引擎使用的核心数据结构和类型

use chrono::{DateTime, Utc};
use ordered_float::OrderedFloat;
use serde::{Deserialize, Serialize};
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use thiserror::Error;

/// 价格计算引擎错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum PriceEngineError {
    #[error("计算错误: {0}")]
    Calculation(String),
    
    #[error("无效输入: {0}")]
    InvalidInput(String),
    
    #[error("缓存错误: {0}")]
    Cache(String),
    
    #[error("池未找到: {pool_id}")]
    PoolNotFound { pool_id: String },
    
    #[error("不支持的代币对: {token_a} -> {token_b}")]
    UnsupportedPair { token_a: String, token_b: String },
    
    #[error("流动性不足")]
    InsufficientLiquidity,
    
    #[error("价格质量问题: {0}")]
    PriceQuality(String),
    
    #[error("超时错误")]
    Timeout,
    
    #[error("状态管理器错误: {0}")]
    StateManager(String),
    
    #[error("数学溢出")]
    MathOverflow,
    
    #[error("网络错误: {0}")]
    Network(String),
    
    #[error("配置错误: {0}")]
    Config(String),
    
    #[error("内部错误: {0}")]
    Internal(String),
}

/// 价格计算引擎结果类型
pub type PriceEngineResult<T> = Result<T, PriceEngineError>;

/// DEX类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DexType {
    /// Raydium CLMM
    RaydiumClmm,
    /// Meteora DLMM
    MeteoraDefi,
    /// 其他DEX类型（预留）
    Other(u8),
}

impl std::fmt::Display for DexType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            DexType::RaydiumClmm => write!(f, "Raydium CLMM"),
            DexType::MeteoraDefi => write!(f, "Meteora DLMM"),
            DexType::Other(id) => write!(f, "Other DEX ({})", id),
        }
    }
}

/// 代币对标识符
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct TokenPair {
    /// Token A 地址
    pub token_a: Pubkey,
    /// Token B 地址
    pub token_b: Pubkey,
}

impl TokenPair {
    /// 创建新的代币对
    pub fn new(token_a: Pubkey, token_b: Pubkey) -> Self {
        Self { token_a, token_b }
    }
    
    /// 获取反向代币对
    pub fn reverse(&self) -> Self {
        Self {
            token_a: self.token_b,
            token_b: self.token_a,
        }
    }
    
    /// 检查是否包含指定代币
    pub fn contains(&self, token: &Pubkey) -> bool {
        &self.token_a == token || &self.token_b == token
    }
}

impl std::fmt::Display for TokenPair {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}/{}", self.token_a, self.token_b)
    }
}

/// 交换估算结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapEstimation {
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 总费用金额
    pub fee_amount: u64,
    /// 协议费用金额
    pub protocol_fee_amount: u64,
    /// 流动性提供者费用金额
    pub lp_fee_amount: u64,
    /// 价格影响（百分比，0-100）
    pub price_impact_percent: f64,
    /// 有效汇率
    pub effective_rate: f64,
    /// 滑点容差（百分比）
    pub slippage_tolerance: f64,
    /// 最小输出金额（考虑滑点）
    pub minimum_output: u64,
    /// 使用的DEX类型
    pub dex_type: DexType,
    /// 池ID
    pub pool_id: Pubkey,
    /// 计算时间戳
    pub timestamp: DateTime<Utc>,
    /// 路径信息（用于跨多个bin/tick的交换）
    pub path_info: Option<SwapPathInfo>,
}

/// 交换路径信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapPathInfo {
    /// 路径步骤
    pub steps: Vec<SwapStep>,
    /// 总跳数
    pub total_hops: usize,
    /// 路径质量评分 (0-100)
    pub quality_score: f64,
}

/// 单个交换步骤
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SwapStep {
    /// 步骤ID（bin ID或tick范围）
    pub step_id: String,
    /// 输入金额
    pub input_amount: u64,
    /// 输出金额
    pub output_amount: u64,
    /// 步骤费用
    pub fee_amount: u64,
    /// 步骤价格
    pub price: f64,
    /// 流动性使用量
    pub liquidity_used: u128,
}

/// 流动性深度信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityDepth {
    /// 买单深度（价格 -> 流动性数量）
    pub bids: Vec<LiquidityLevel>,
    /// 卖单深度（价格 -> 流动性数量）
    pub asks: Vec<LiquidityLevel>,
    /// 总流动性
    pub total_liquidity: u128,
    /// 价格范围
    pub price_range: PriceRange,
    /// 活跃价格区间数量
    pub active_levels: usize,
    /// 计算时间戳
    pub timestamp: DateTime<Utc>,
}

/// 流动性等级
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LiquidityLevel {
    /// 价格
    pub price: OrderedFloat<f64>,
    /// 流动性数量
    pub liquidity: u128,
    /// 累计流动性
    pub cumulative_liquidity: u128,
    /// 深度百分比
    pub depth_percent: f64,
}

/// 价格范围
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceRange {
    /// 最低价格
    pub min_price: f64,
    /// 最高价格
    pub max_price: f64,
    /// 当前价格
    pub current_price: f64,
    /// 价格中位数
    pub median_price: f64,
}

/// 价格报价
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceQuote {
    /// 代币对
    pub token_pair: TokenPair,
    /// 当前现货价格
    pub spot_price: f64,
    /// 买入价格
    pub bid_price: f64,
    /// 卖出价格
    pub ask_price: f64,
    /// 价差（百分比）
    pub spread_percent: f64,
    /// 24小时价格变化（百分比）
    pub price_change_24h: Option<f64>,
    /// 24小时交易量
    pub volume_24h: Option<u64>,
    /// 总锁定价值
    pub tvl: Option<u64>,
    /// 数据源
    pub source: DexType,
    /// 池ID
    pub pool_id: Pubkey,
    /// 价格质量评分 (0-100)
    pub quality_score: f64,
    /// 置信区间
    pub confidence_interval: ConfidenceInterval,
    /// 时间戳
    pub timestamp: DateTime<Utc>,
    /// 数据有效期
    pub expires_at: DateTime<Utc>,
}

/// 置信区间
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConfidenceInterval {
    /// 置信水平（例如0.95表示95%置信度）
    pub confidence_level: f64,
    /// 下界
    pub lower_bound: f64,
    /// 上界
    pub upper_bound: f64,
}

/// 价格请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceRequest {
    /// 代币对
    pub token_pair: TokenPair,
    /// 交换方向（true: token_a -> token_b, false: token_b -> token_a）
    pub swap_direction: bool,
    /// 输入金额（可选，用于计算有效价格）
    pub input_amount: Option<u64>,
    /// 请求的价格类型
    pub price_types: Vec<PriceType>,
    /// DEX类型偏好
    pub dex_preference: Option<Vec<DexType>>,
    /// 最大滑点容忍度（百分比）
    pub max_slippage: Option<f64>,
    /// 缓存容忍度（秒）
    pub cache_tolerance: Option<u64>,
    /// 请求时间戳
    pub timestamp: DateTime<Utc>,
}

/// 价格类型枚举
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum PriceType {
    /// 现货价格
    Spot,
    /// 有效价格（考虑交换量）
    Effective,
    /// 加权平均价格
    WeightedAverage,
    /// 最佳执行价格
    BestExecution,
}

/// 批量价格请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchPriceRequest {
    /// 价格请求列表
    pub requests: Vec<PriceRequest>,
    /// 并行处理限制
    pub concurrency_limit: Option<usize>,
    /// 超时设置（秒）
    pub timeout_seconds: Option<u64>,
}

/// 批量价格响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchPriceResponse {
    /// 价格报价列表
    pub quotes: Vec<PriceEngineResult<PriceQuote>>,
    /// 处理统计
    pub stats: BatchProcessingStats,
}

/// 批量处理统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BatchProcessingStats {
    /// 请求总数
    pub total_requests: usize,
    /// 成功数量
    pub successful: usize,
    /// 失败数量
    pub failed: usize,
    /// 缓存命中数量
    pub cache_hits: usize,
    /// 处理时间（毫秒）
    pub processing_time_ms: u64,
    /// 平均延迟（毫秒）
    pub average_latency_ms: f64,
}

/// 价格更新事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceUpdateEvent {
    /// 池ID
    pub pool_id: Pubkey,
    /// DEX类型
    pub dex_type: DexType,
    /// 代币对
    pub token_pair: TokenPair,
    /// 更新前价格
    pub previous_price: Option<f64>,
    /// 更新后价格
    pub current_price: f64,
    /// 价格变化百分比
    pub price_change_percent: f64,
    /// 更新触发器
    pub trigger: PriceUpdateTrigger,
    /// 事件时间戳
    pub timestamp: DateTime<Utc>,
    /// 块高度
    pub slot: u64,
}

/// 价格更新触发器
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PriceUpdateTrigger {
    /// 池状态变化
    PoolStateChange,
    /// 新交易
    NewTransaction,
    /// 流动性变化
    LiquidityChange,
    /// 定时更新
    ScheduledUpdate,
    /// 手动刷新
    ManualRefresh,
}

/// 价格质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceQualityMetrics {
    /// 数据新鲜度评分 (0-100)
    pub freshness_score: f64,
    /// 流动性深度评分 (0-100)
    pub liquidity_score: f64,
    /// 价格稳定性评分 (0-100)
    pub stability_score: f64,
    /// 成交量评分 (0-100)
    pub volume_score: f64,
    /// 总体质量评分 (0-100)
    pub overall_score: f64,
    /// 数据来源可靠性
    pub source_reliability: f64,
    /// 最后评估时间
    pub last_evaluated: DateTime<Utc>,
    /// 质量标签
    pub quality_flags: Vec<QualityFlag>,
}

/// 质量标志
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum QualityFlag {
    /// 高质量数据
    HighQuality,
    /// 流动性低
    LowLiquidity,
    /// 价格波动大
    HighVolatility,
    /// 数据过时
    Stale,
    /// 异常价格
    Anomalous,
    /// 可疑活动
    Suspicious,
}

/// 市场数据快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketSnapshot {
    /// 所有代币对的价格
    pub prices: HashMap<TokenPair, PriceQuote>,
    /// 市场统计
    pub market_stats: MarketStats,
    /// 快照时间戳
    pub timestamp: DateTime<Utc>,
    /// 快照版本
    pub version: u64,
}

/// 市场统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MarketStats {
    /// 活跃池数量
    pub active_pools: usize,
    /// 总锁定价值
    pub total_tvl: u64,
    /// 24小时总交易量
    pub total_volume_24h: u64,
    /// 平均价格质量评分
    pub average_quality_score: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 价格计算配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriceCalculationConfig {
    /// 计算超时（毫秒）
    pub calculation_timeout_ms: u64,
    /// 最大滑点容忍度（百分比）
    pub max_slippage_percent: f64,
    /// 价格新鲜度阈值（秒）
    pub freshness_threshold_seconds: u64,
    /// 质量分数最低阈值
    pub min_quality_score: f64,
    /// 流动性深度计算层数
    pub liquidity_depth_levels: usize,
    /// 启用价格平滑
    pub enable_price_smoothing: bool,
    /// 价格平滑窗口大小
    pub smoothing_window_size: usize,
    /// 异常检测敏感度
    pub anomaly_detection_sensitivity: f64,
}

impl Default for PriceCalculationConfig {
    fn default() -> Self {
        Self {
            calculation_timeout_ms: 100,
            max_slippage_percent: 5.0,
            freshness_threshold_seconds: 60,
            min_quality_score: 70.0,
            liquidity_depth_levels: 10,
            enable_price_smoothing: true,
            smoothing_window_size: 5,
            anomaly_detection_sensitivity: 2.0,
        }
    }
}

/// 统计累积器，用于性能监控
#[derive(Debug, Clone, Default)]
pub struct PerformanceStats {
    /// 计算次数
    pub calculation_count: u64,
    /// 总计算时间（纳秒）
    pub total_calculation_time_ns: u64,
    /// 缓存命中次数
    pub cache_hits: u64,
    /// 缓存未命中次数
    pub cache_misses: u64,
    /// 错误次数
    pub error_count: u64,
    /// 最后重置时间
    pub last_reset: DateTime<Utc>,
}

impl PerformanceStats {
    /// 创建新的性能统计
    pub fn new() -> Self {
        Self {
            last_reset: Utc::now(),
            ..Default::default()
        }
    }
    
    /// 计算平均计算时间（纳秒）
    pub fn average_calculation_time_ns(&self) -> f64 {
        if self.calculation_count == 0 {
            0.0
        } else {
            self.total_calculation_time_ns as f64 / self.calculation_count as f64
        }
    }
    
    /// 计算缓存命中率
    pub fn cache_hit_rate(&self) -> f64 {
        let total = self.cache_hits + self.cache_misses;
        if total == 0 {
            0.0
        } else {
            self.cache_hits as f64 / total as f64
        }
    }
    
    /// 计算错误率
    pub fn error_rate(&self) -> f64 {
        let total = self.calculation_count + self.error_count;
        if total == 0 {
            0.0
        } else {
            self.error_count as f64 / total as f64
        }
    }
    
    /// 重置统计数据
    pub fn reset(&mut self) {
        *self = Self::new();
    }
}