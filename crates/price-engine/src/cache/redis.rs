//! Redis Price Cache Implementation
//!
//! Redis价格缓存实现，提供分布式缓存支持

#[cfg(feature = "redis-cache")]
use async_trait::async_trait;
#[cfg(feature = "redis-cache")]
use chrono::{DateTime, Utc};
#[cfg(feature = "redis-cache")]
use redis::{Client, Commands, ConnectionManager, RedisResult};
#[cfg(feature = "redis-cache")]
use serde_json;
#[cfg(feature = "redis-cache")]
use std::sync::atomic::{AtomicU64, Ordering};
#[cfg(feature = "redis-cache")]
use std::sync::Arc;
#[cfg(feature = "redis-cache")]
use std::time::Duration;
#[cfg(feature = "redis-cache")]
use tokio::sync::RwLock;
#[cfg(feature = "redis-cache")]
use tracing::{debug, error, warn};

#[cfg(feature = "redis-cache")]
use super::{CacheConfig, CacheError, CacheStats, PriceCache};
#[cfg(feature = "redis-cache")]
use crate::types::PriceQuote;

/// Redis价格缓存实现
/// 
/// 提供分布式缓存支持，适合多实例部署场景
#[cfg(feature = "redis-cache")]
#[derive(Debug)]
pub struct RedisPriceCache {
    /// Redis连接管理器
    connection_manager: ConnectionManager,
    /// 配置
    config: CacheConfig,
    /// 统计信息
    stats: Arc<RwLock<CacheStats>>,
    /// 原子计数器
    hit_counter: AtomicU64,
    miss_counter: AtomicU64,
    set_counter: AtomicU64,
    delete_counter: AtomicU64,
}

#[cfg(feature = "redis-cache")]
impl RedisPriceCache {
    /// 创建新的Redis价格缓存
    /// 
    /// # 参数
    /// * `redis_url` - Redis连接URL
    /// * `config` - 缓存配置
    pub async fn new(redis_url: &str, config: CacheConfig) -> Result<Self, CacheError> {
        let client = Client::open(redis_url)
            .map_err(|e| CacheError::Connection(format!("连接Redis失败: {}", e)))?;

        let connection_manager = ConnectionManager::new(client)
            .await
            .map_err(|e| CacheError::Connection(format!("创建连接管理器失败: {}", e)))?;

        Ok(Self {
            connection_manager,
            config,
            stats: Arc::new(RwLock::new(CacheStats::new())),
            hit_counter: AtomicU64::new(0),
            miss_counter: AtomicU64::new(0),
            set_counter: AtomicU64::new(0),
            delete_counter: AtomicU64::new(0),
        })
    }

    /// 生成带前缀的键
    fn prefixed_key(&self, key: &str) -> String {
        format!("{}:{}", self.config.key_prefix, key)
    }

    /// 序列化价格报价
    fn serialize_quote(&self, quote: &PriceQuote) -> Result<String, CacheError> {
        if self.config.enable_compression {
            // 使用简化的序列化以减少大小
            let simplified = serde_json::json!({
                "tp": format!("{}:{}", quote.token_pair.token_a, quote.token_pair.token_b),
                "sp": quote.spot_price,
                "bp": quote.bid_price,
                "ap": quote.ask_price,
                "spr": quote.spread_percent,
                "pc": quote.price_change_24h,
                "vol": quote.volume_24h,
                "tvl": quote.tvl,
                "src": quote.source as u8,
                "pid": quote.pool_id.to_string(),
                "qs": quote.quality_score,
                "ci": [quote.confidence_interval.lower_bound, quote.confidence_interval.upper_bound],
                "ts": quote.timestamp.timestamp(),
                "exp": quote.expires_at.timestamp(),
            });
            serde_json::to_string(&simplified)
                .map_err(|e| CacheError::Serialization(e.to_string()))
        } else {
            serde_json::to_string(quote)
                .map_err(|e| CacheError::Serialization(e.to_string()))
        }
    }

    /// 反序列化价格报价
    fn deserialize_quote(&self, data: &str) -> Result<PriceQuote, CacheError> {
        if self.config.enable_compression {
            // 反序列化简化格式
            let simplified: serde_json::Value = serde_json::from_str(data)
                .map_err(|e| CacheError::Deserialization(e.to_string()))?;

            let token_pair_str = simplified["tp"].as_str()
                .ok_or_else(|| CacheError::Deserialization("缺少token_pair".to_string()))?;
            let tokens: Vec<&str> = token_pair_str.split(':').collect();
            if tokens.len() != 2 {
                return Err(CacheError::Deserialization("无效的token_pair格式".to_string()));
            }

            let token_a = tokens[0].parse()
                .map_err(|e| CacheError::Deserialization(format!("无效的token_a: {}", e)))?;
            let token_b = tokens[1].parse()
                .map_err(|e| CacheError::Deserialization(format!("无效的token_b: {}", e)))?;

            let confidence_bounds = simplified["ci"].as_array()
                .ok_or_else(|| CacheError::Deserialization("缺少置信区间".to_string()))?;

            Ok(PriceQuote {
                token_pair: crate::types::TokenPair::new(token_a, token_b),
                spot_price: simplified["sp"].as_f64().unwrap_or(0.0),
                bid_price: simplified["bp"].as_f64().unwrap_or(0.0),
                ask_price: simplified["ap"].as_f64().unwrap_or(0.0),
                spread_percent: simplified["spr"].as_f64().unwrap_or(0.0),
                price_change_24h: simplified["pc"].as_f64(),
                volume_24h: simplified["vol"].as_u64(),
                tvl: simplified["tvl"].as_u64(),
                source: match simplified["src"].as_u64().unwrap_or(0) {
                    0 => crate::types::DexType::RaydiumClmm,
                    1 => crate::types::DexType::MeteoraDefi,
                    n => crate::types::DexType::Other(n as u8),
                },
                pool_id: simplified["pid"].as_str().unwrap_or("11111111111111111111111111111112")
                    .parse().unwrap_or_default(),
                quality_score: simplified["qs"].as_f64().unwrap_or(0.0),
                confidence_interval: crate::types::ConfidenceInterval {
                    confidence_level: 0.95,
                    lower_bound: confidence_bounds[0].as_f64().unwrap_or(0.0),
                    upper_bound: confidence_bounds[1].as_f64().unwrap_or(0.0),
                },
                timestamp: DateTime::from_timestamp(
                    simplified["ts"].as_i64().unwrap_or(0), 0
                ).unwrap_or(Utc::now()),
                expires_at: DateTime::from_timestamp(
                    simplified["exp"].as_i64().unwrap_or(0), 0
                ).unwrap_or(Utc::now()),
            })
        } else {
            serde_json::from_str(data)
                .map_err(|e| CacheError::Deserialization(e.to_string()))
        }
    }

    /// 更新统计信息
    async fn update_stats(&self) {
        let mut stats = self.stats.write().await;
        stats.hits = self.hit_counter.load(Ordering::Relaxed);
        stats.misses = self.miss_counter.load(Ordering::Relaxed);
        stats.sets = self.set_counter.load(Ordering::Relaxed);
        stats.deletes = self.delete_counter.load(Ordering::Relaxed);
        stats.update_hit_rate();
    }

    /// 执行Redis命令的辅助函数
    async fn execute_redis_command<F, R>(&self, command: F) -> Result<R, CacheError>
    where
        F: FnOnce(&mut ConnectionManager) -> RedisResult<R> + Send,
        R: Send,
    {
        let mut conn = self.connection_manager.clone();
        tokio::task::spawn_blocking(move || command(&mut conn))
            .await
            .map_err(|e| CacheError::Internal(format!("任务执行失败: {}", e)))?
            .map_err(|e| CacheError::Redis(e.to_string()))
    }
}

#[cfg(feature = "redis-cache")]
#[async_trait]
impl PriceCache for RedisPriceCache {
    async fn get(&self, key: &str) -> Result<Option<PriceQuote>, CacheError> {
        let prefixed_key = self.prefixed_key(key);
        
        let result: Option<String> = self.execute_redis_command(|conn| {
            conn.get(&prefixed_key)
        }).await?;

        match result {
            Some(data) => {
                match self.deserialize_quote(&data) {
                    Ok(quote) => {
                        // 检查是否过期
                        if quote.expires_at > Utc::now() {
                            self.hit_counter.fetch_add(1, Ordering::Relaxed);
                            Ok(Some(quote))
                        } else {
                            // 删除过期项
                            let _ = self.delete(key).await;
                            self.miss_counter.fetch_add(1, Ordering::Relaxed);
                            Ok(None)
                        }
                    }
                    Err(e) => {
                        error!("反序列化缓存数据失败: {}", e);
                        // 删除损坏的数据
                        let _ = self.delete(key).await;
                        self.miss_counter.fetch_add(1, Ordering::Relaxed);
                        Ok(None)
                    }
                }
            }
            None => {
                self.miss_counter.fetch_add(1, Ordering::Relaxed);
                Ok(None)
            }
        }
    }

    async fn set(&self, key: &str, value: &PriceQuote, ttl: Duration) -> Result<(), CacheError> {
        let prefixed_key = self.prefixed_key(key);
        let serialized = self.serialize_quote(value)?;
        let ttl_seconds = ttl.as_secs() as i64;

        self.execute_redis_command(move |conn| {
            conn.set_ex(&prefixed_key, &serialized, ttl_seconds)
        }).await?;

        self.set_counter.fetch_add(1, Ordering::Relaxed);
        Ok(())
    }

    async fn delete(&self, key: &str) -> Result<bool, CacheError> {
        let prefixed_key = self.prefixed_key(key);

        let deleted: i32 = self.execute_redis_command(|conn| {
            conn.del(&prefixed_key)
        }).await?;

        let existed = deleted > 0;
        if existed {
            self.delete_counter.fetch_add(1, Ordering::Relaxed);
        }
        Ok(existed)
    }

    async fn exists(&self, key: &str) -> Result<bool, CacheError> {
        let prefixed_key = self.prefixed_key(key);

        let exists: bool = self.execute_redis_command(|conn| {
            conn.exists(&prefixed_key)
        }).await?;

        Ok(exists)
    }

    async fn expire(&self, key: &str, ttl: Duration) -> Result<bool, CacheError> {
        let prefixed_key = self.prefixed_key(key);
        let ttl_seconds = ttl.as_secs() as i64;

        let result: bool = self.execute_redis_command(|conn| {
            conn.expire(&prefixed_key, ttl_seconds)
        }).await?;

        Ok(result)
    }

    async fn ttl(&self, key: &str) -> Result<Option<Duration>, CacheError> {
        let prefixed_key = self.prefixed_key(key);

        let ttl_seconds: i64 = self.execute_redis_command(|conn| {
            conn.ttl(&prefixed_key)
        }).await?;

        match ttl_seconds {
            -2 => Ok(None), // Key不存在
            -1 => Ok(Some(Duration::from_secs(u64::MAX))), // 没有过期时间
            n if n > 0 => Ok(Some(Duration::from_secs(n as u64))),
            _ => Ok(Some(Duration::from_secs(0))), // 已过期
        }
    }

    async fn mget(&self, keys: &[String]) -> Result<Vec<Option<PriceQuote>>, CacheError> {
        if keys.is_empty() {
            return Ok(Vec::new());
        }

        let prefixed_keys: Vec<String> = keys.iter()
            .map(|k| self.prefixed_key(k))
            .collect();

        let results: Vec<Option<String>> = self.execute_redis_command(|conn| {
            conn.get(&prefixed_keys)
        }).await?;

        let mut quotes = Vec::with_capacity(results.len());
        for result in results {
            match result {
                Some(data) => {
                    match self.deserialize_quote(&data) {
                        Ok(quote) => {
                            if quote.expires_at > Utc::now() {
                                quotes.push(Some(quote));
                                self.hit_counter.fetch_add(1, Ordering::Relaxed);
                            } else {
                                quotes.push(None);
                                self.miss_counter.fetch_add(1, Ordering::Relaxed);
                            }
                        }
                        Err(_) => {
                            quotes.push(None);
                            self.miss_counter.fetch_add(1, Ordering::Relaxed);
                        }
                    }
                }
                None => {
                    quotes.push(None);
                    self.miss_counter.fetch_add(1, Ordering::Relaxed);
                }
            }
        }

        Ok(quotes)
    }

    async fn mset(&self, items: &[(String, PriceQuote)], ttl: Duration) -> Result<(), CacheError> {
        if items.is_empty() {
            return Ok(());
        }

        let ttl_seconds = ttl.as_secs() as i64;
        
        for (key, value) in items {
            self.set(key, value, ttl).await?;
        }

        Ok(())
    }

    async fn clear(&self) -> Result<(), CacheError> {
        let pattern = format!("{}:*", self.config.key_prefix);

        // 使用SCAN来安全地删除所有匹配的键
        let keys: Vec<String> = self.execute_redis_command(|conn| {
            let mut keys = Vec::new();
            let mut cursor = 0;
            loop {
                let result: (i32, Vec<String>) = redis::cmd("SCAN")
                    .arg(cursor)
                    .arg("MATCH")
                    .arg(&pattern)
                    .arg("COUNT")
                    .arg(1000)
                    .query(conn)?;
                
                cursor = result.0;
                keys.extend(result.1);
                
                if cursor == 0 {
                    break;
                }
            }
            Ok::<Vec<String>, redis::RedisError>(keys)
        }).await?;

        if !keys.is_empty() {
            let deleted: i32 = self.execute_redis_command(|conn| {
                conn.del(&keys)
            }).await?;

            self.delete_counter.fetch_add(deleted as u64, Ordering::Relaxed);
        }

        Ok(())
    }

    async fn stats(&self) -> Result<CacheStats, CacheError> {
        self.update_stats().await;
        
        // 尝试获取Redis内存使用信息
        let info: String = self.execute_redis_command(|conn| {
            redis::cmd("INFO").arg("memory").query(conn)
        }).await.unwrap_or_default();

        let mut stats = self.stats.read().await.clone();
        
        // 解析Redis内存信息
        for line in info.lines() {
            if line.starts_with("used_memory:") {
                if let Some(memory_str) = line.split(':').nth(1) {
                    if let Ok(memory) = memory_str.parse::<usize>() {
                        stats.memory_usage = memory;
                        break;
                    }
                }
            }
        }

        Ok(stats)
    }

    async fn size(&self) -> Result<usize, CacheError> {
        let pattern = format!("{}:*", self.config.key_prefix);
        
        let count: usize = self.execute_redis_command(|conn| {
            let mut count = 0;
            let mut cursor = 0;
            loop {
                let result: (i32, Vec<String>) = redis::cmd("SCAN")
                    .arg(cursor)
                    .arg("MATCH")
                    .arg(&pattern)
                    .arg("COUNT")
                    .arg(1000)
                    .query(conn)?;
                
                cursor = result.0;
                count += result.1.len();
                
                if cursor == 0 {
                    break;
                }
            }
            Ok::<usize, redis::RedisError>(count)
        }).await?;

        Ok(count)
    }

    async fn health_check(&self) -> Result<bool, CacheError> {
        // 执行PING命令检查Redis连接
        let pong: String = self.execute_redis_command(|conn| {
            redis::cmd("PING").query(conn)
        }).await?;

        Ok(pong == "PONG")
    }

    async fn cleanup(&self) -> Result<usize, CacheError> {
        // Redis会自动清理过期的键，这里我们只是返回0
        // 在实际场景中，可以实现更复杂的清理逻辑
        Ok(0)
    }
}

// 为非Redis功能提供存根实现
#[cfg(not(feature = "redis-cache"))]
pub struct RedisPriceCache;

#[cfg(not(feature = "redis-cache"))]
impl RedisPriceCache {
    pub async fn new(_redis_url: &str, _config: super::CacheConfig) -> Result<Self, super::CacheError> {
        Err(super::CacheError::Internal(
            "Redis缓存功能未启用，请使用redis-cache feature".to_string()
        ))
    }
}

#[cfg(test)]
#[cfg(feature = "redis-cache")]
mod tests {
    use super::*;
    use crate::types::{TokenPair, DexType, ConfidenceInterval};
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    fn create_test_quote() -> PriceQuote {
        PriceQuote {
            token_pair: TokenPair::new(
                Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
                Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            ),
            spot_price: 100.0,
            bid_price: 99.9,
            ask_price: 100.1,
            spread_percent: 0.2,
            price_change_24h: Some(5.5),
            volume_24h: Some(1000000),
            tvl: Some(50000000),
            source: DexType::RaydiumClmm,
            pool_id: Pubkey::default(),
            quality_score: 95.0,
            confidence_interval: ConfidenceInterval {
                confidence_level: 0.95,
                lower_bound: 95.0,
                upper_bound: 105.0,
            },
            timestamp: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::seconds(60),
        }
    }

    #[tokio::test]
    #[ignore] // 需要Redis服务器运行
    async fn test_redis_cache_basic_operations() {
        let config = CacheConfig::default();
        let cache = RedisPriceCache::new("redis://localhost:6379", config)
            .await.unwrap();
        
        let key = "test_key";
        let quote = create_test_quote();
        
        // 清理可能存在的数据
        let _ = cache.delete(key).await;
        
        // 测试设置和获取
        cache.set(key, &quote, Duration::from_secs(60)).await.unwrap();
        let retrieved = cache.get(key).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().spot_price, quote.spot_price);
        
        // 测试存在性检查
        assert!(cache.exists(key).await.unwrap());
        
        // 测试删除
        assert!(cache.delete(key).await.unwrap());
        assert!(!cache.exists(key).await.unwrap());
    }

    #[tokio::test]
    #[ignore] // 需要Redis服务器运行
    async fn test_redis_cache_compression() {
        let mut config = CacheConfig::default();
        config.enable_compression = true;
        
        let cache = RedisPriceCache::new("redis://localhost:6379", config)
            .await.unwrap();
        
        let quote = create_test_quote();
        
        // 测试压缩序列化和反序列化
        let serialized = cache.serialize_quote(&quote).unwrap();
        let deserialized = cache.deserialize_quote(&serialized).unwrap();
        
        assert_eq!(quote.spot_price, deserialized.spot_price);
        assert_eq!(quote.token_pair.token_a, deserialized.token_pair.token_a);
        assert_eq!(quote.token_pair.token_b, deserialized.token_pair.token_b);
    }

    #[tokio::test]
    #[ignore] // 需要Redis服务器运行
    async fn test_redis_cache_health_check() {
        let config = CacheConfig::default();
        let cache = RedisPriceCache::new("redis://localhost:6379", config)
            .await.unwrap();
        
        let is_healthy = cache.health_check().await.unwrap();
        assert!(is_healthy);
    }
}