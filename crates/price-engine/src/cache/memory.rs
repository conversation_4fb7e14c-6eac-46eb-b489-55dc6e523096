//! Memory Price Cache Implementation
//!
//! 内存价格缓存实现，使用DashMap提供并发安全的高性能缓存

use async_trait::async_trait;
use chrono::Utc;
use dashmap::DashMap;
use std::sync::atomic::{AtomicU64, Ordering};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::time::{interval, Interval};
use tracing::{debug, warn};

use super::{CacheConfig, CacheEntry, CacheError, CacheStats, PriceCache};
use crate::types::PriceQuote;

/// 内存价格缓存实现
/// 
/// 使用DashMap提供线程安全的高性能内存缓存
#[derive(Debug)]
pub struct MemoryPriceCache {
    /// 缓存存储
    storage: DashMap<String, CacheEntry<PriceQuote>>,
    /// 配置
    config: CacheConfig,
    /// 统计信息
    stats: Arc<RwLock<CacheStats>>,
    /// 原子计数器
    hit_counter: AtomicU64,
    miss_counter: AtomicU64,
    set_counter: AtomicU64,
    delete_counter: AtomicU64,
    /// 清理任务句柄
    cleanup_handle: Option<tokio::task::JoinHandle<()>>,
}

impl MemoryPriceCache {
    /// 创建新的内存价格缓存
    pub fn new(config: CacheConfig) -> Self {
        let cache = Self {
            storage: DashMap::new(),
            config: config.clone(),
            stats: Arc::new(RwLock::new(CacheStats::new())),
            hit_counter: AtomicU64::new(0),
            miss_counter: AtomicU64::new(0),
            set_counter: AtomicU64::new(0),
            delete_counter: AtomicU64::new(0),
            cleanup_handle: None,
        };

        cache
    }

    /// 启动后台清理任务
    pub async fn start_cleanup_task(&mut self) {
        let storage = self.storage.clone();
        let stats = self.stats.clone();
        let interval_seconds = self.config.cleanup_interval_seconds;

        let handle = tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(interval_seconds));
            
            loop {
                interval.tick().await;
                
                let expired_count = Self::cleanup_expired_entries(&storage).await;
                if expired_count > 0 {
                    debug!("清理了 {} 个过期缓存项", expired_count);
                    
                    // 更新统计
                    let mut stats_guard = stats.write().await;
                    stats_guard.expired += expired_count as u64;
                    stats_guard.current_size = storage.len();
                }
            }
        });

        self.cleanup_handle = Some(handle);
    }

    /// 停止清理任务
    pub fn stop_cleanup_task(&mut self) {
        if let Some(handle) = self.cleanup_handle.take() {
            handle.abort();
        }
    }

    /// 清理过期项
    async fn cleanup_expired_entries(storage: &DashMap<String, CacheEntry<PriceQuote>>) -> usize {
        let mut expired_keys = Vec::new();
        
        // 找出所有过期的键
        for entry in storage.iter() {
            if entry.value().is_expired() {
                expired_keys.push(entry.key().clone());
            }
        }
        
        // 删除过期的键
        let count = expired_keys.len();
        for key in expired_keys {
            storage.remove(&key);
        }
        
        count
    }

    /// 检查并删除过期项
    fn evict_if_expired(&self, key: &str) -> bool {
        if let Some(entry) = self.storage.get(key) {
            if entry.is_expired() {
                drop(entry); // 释放读锁
                self.storage.remove(key);
                return true;
            }
        }
        false
    }

    /// 检查是否需要淘汰缓存项
    fn should_evict(&self) -> bool {
        self.storage.len() >= self.config.max_size
    }

    /// 淘汰最少使用的缓存项
    async fn evict_lru(&self) -> Result<(), CacheError> {
        if self.storage.is_empty() {
            return Ok(());
        }

        // 找到最少使用的项
        let mut min_access_count = u64::MAX;
        let mut oldest_time = Utc::now();
        let mut lru_key: Option<String> = None;

        for entry in self.storage.iter() {
            let cache_entry = entry.value();
            if cache_entry.access_count < min_access_count || 
               (cache_entry.access_count == min_access_count && cache_entry.last_accessed < oldest_time) {
                min_access_count = cache_entry.access_count;
                oldest_time = cache_entry.last_accessed;
                lru_key = Some(entry.key().clone());
            }
        }

        if let Some(key) = lru_key {
            self.storage.remove(&key);
            self.delete_counter.fetch_add(1, Ordering::Relaxed);
        }

        Ok(())
    }

    /// 更新统计信息
    async fn update_stats(&self) {
        let mut stats = self.stats.write().await;
        stats.hits = self.hit_counter.load(Ordering::Relaxed);
        stats.misses = self.miss_counter.load(Ordering::Relaxed);
        stats.sets = self.set_counter.load(Ordering::Relaxed);
        stats.deletes = self.delete_counter.load(Ordering::Relaxed);
        stats.current_size = self.storage.len();
        stats.max_size = self.config.max_size;
        stats.update_hit_rate();
        
        // 估算内存使用量
        stats.memory_usage = self.storage.len() * std::mem::size_of::<CacheEntry<PriceQuote>>();
    }
}

#[async_trait]
impl PriceCache for MemoryPriceCache {
    async fn get(&self, key: &str) -> Result<Option<PriceQuote>, CacheError> {
        // 首先检查并清理过期项
        if self.evict_if_expired(key) {
            self.miss_counter.fetch_add(1, Ordering::Relaxed);
            return Ok(None);
        }

        match self.storage.get_mut(key) {
            Some(mut entry) => {
                if entry.is_expired() {
                    // 双重检查过期状态
                    drop(entry);
                    self.storage.remove(key);
                    self.miss_counter.fetch_add(1, Ordering::Relaxed);
                    Ok(None)
                } else {
                    // 更新访问信息
                    entry.mark_accessed();
                    let quote = entry.data.clone();
                    
                    self.hit_counter.fetch_add(1, Ordering::Relaxed);
                    Ok(Some(quote))
                }
            }
            None => {
                self.miss_counter.fetch_add(1, Ordering::Relaxed);
                Ok(None)
            }
        }
    }

    async fn set(&self, key: &str, value: &PriceQuote, ttl: Duration) -> Result<(), CacheError> {
        // 检查是否需要淘汰
        if self.should_evict() && !self.storage.contains_key(key) {
            self.evict_lru().await?;
        }

        let cache_entry = CacheEntry::new(value.clone(), ttl);
        self.storage.insert(key.to_string(), cache_entry);
        self.set_counter.fetch_add(1, Ordering::Relaxed);

        Ok(())
    }

    async fn delete(&self, key: &str) -> Result<bool, CacheError> {
        let existed = self.storage.remove(key).is_some();
        if existed {
            self.delete_counter.fetch_add(1, Ordering::Relaxed);
        }
        Ok(existed)
    }

    async fn exists(&self, key: &str) -> Result<bool, CacheError> {
        if self.evict_if_expired(key) {
            return Ok(false);
        }
        Ok(self.storage.contains_key(key))
    }

    async fn expire(&self, key: &str, ttl: Duration) -> Result<bool, CacheError> {
        match self.storage.get_mut(key) {
            Some(mut entry) => {
                let now = Utc::now();
                entry.expires_at = now + chrono::Duration::from_std(ttl)
                    .unwrap_or(chrono::Duration::seconds(60));
                Ok(true)
            }
            None => Ok(false),
        }
    }

    async fn ttl(&self, key: &str) -> Result<Option<Duration>, CacheError> {
        match self.storage.get(key) {
            Some(entry) => {
                if entry.is_expired() {
                    Ok(Some(Duration::from_secs(0)))
                } else {
                    let remaining = entry.expires_at - Utc::now();
                    let duration = remaining.to_std().unwrap_or(Duration::from_secs(0));
                    Ok(Some(duration))
                }
            }
            None => Ok(None),
        }
    }

    async fn mget(&self, keys: &[String]) -> Result<Vec<Option<PriceQuote>>, CacheError> {
        let mut results = Vec::with_capacity(keys.len());
        
        for key in keys {
            results.push(self.get(key).await?);
        }
        
        Ok(results)
    }

    async fn mset(&self, items: &[(String, PriceQuote)], ttl: Duration) -> Result<(), CacheError> {
        for (key, value) in items {
            self.set(key, value, ttl).await?;
        }
        Ok(())
    }

    async fn clear(&self) -> Result<(), CacheError> {
        let count = self.storage.len();
        self.storage.clear();
        self.delete_counter.fetch_add(count as u64, Ordering::Relaxed);
        Ok(())
    }

    async fn stats(&self) -> Result<CacheStats, CacheError> {
        self.update_stats().await;
        let stats = self.stats.read().await;
        Ok(stats.clone())
    }

    async fn size(&self) -> Result<usize, CacheError> {
        Ok(self.storage.len())
    }

    async fn health_check(&self) -> Result<bool, CacheError> {
        // 简单的健康检查：验证缓存是否响应
        let test_key = format!("{}:health_check", self.config.key_prefix);
        let test_quote = PriceQuote {
            token_pair: crate::types::TokenPair::new(
                solana_sdk::pubkey::Pubkey::default(),
                solana_sdk::pubkey::Pubkey::default(),
            ),
            spot_price: 1.0,
            bid_price: 0.999,
            ask_price: 1.001,
            spread_percent: 0.2,
            price_change_24h: None,
            volume_24h: None,
            tvl: None,
            source: crate::types::DexType::RaydiumClmm,
            pool_id: solana_sdk::pubkey::Pubkey::default(),
            quality_score: 100.0,
            confidence_interval: crate::types::ConfidenceInterval {
                confidence_level: 0.95,
                lower_bound: 0.95,
                upper_bound: 1.05,
            },
            timestamp: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::seconds(1),
        };

        // 测试设置和获取
        self.set(&test_key, &test_quote, Duration::from_secs(1)).await?;
        let retrieved = self.get(&test_key).await?;
        self.delete(&test_key).await?;

        Ok(retrieved.is_some())
    }

    async fn cleanup(&self) -> Result<usize, CacheError> {
        let count = Self::cleanup_expired_entries(&self.storage).await;
        
        // 更新统计
        let mut stats = self.stats.write().await;
        stats.expired += count as u64;
        stats.current_size = self.storage.len();
        
        Ok(count)
    }
}

impl Drop for MemoryPriceCache {
    fn drop(&mut self) {
        self.stop_cleanup_task();
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{TokenPair, DexType, ConfidenceInterval};
    use solana_sdk::pubkey::Pubkey;
    use std::str::FromStr;

    fn create_test_quote() -> PriceQuote {
        PriceQuote {
            token_pair: TokenPair::new(
                Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap(),
                Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v").unwrap(),
            ),
            spot_price: 100.0,
            bid_price: 99.9,
            ask_price: 100.1,
            spread_percent: 0.2,
            price_change_24h: Some(5.5),
            volume_24h: Some(1000000),
            tvl: Some(50000000),
            source: DexType::RaydiumClmm,
            pool_id: Pubkey::default(),
            quality_score: 95.0,
            confidence_interval: ConfidenceInterval {
                confidence_level: 0.95,
                lower_bound: 95.0,
                upper_bound: 105.0,
            },
            timestamp: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::seconds(60),
        }
    }

    #[tokio::test]
    async fn test_memory_cache_basic_operations() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let key = "test_key";
        let quote = create_test_quote();
        
        // 测试设置和获取
        cache.set(key, &quote, Duration::from_secs(60)).await.unwrap();
        let retrieved = cache.get(key).await.unwrap();
        assert!(retrieved.is_some());
        assert_eq!(retrieved.unwrap().spot_price, quote.spot_price);
        
        // 测试存在性检查
        assert!(cache.exists(key).await.unwrap());
        
        // 测试删除
        assert!(cache.delete(key).await.unwrap());
        assert!(!cache.exists(key).await.unwrap());
    }

    #[tokio::test]
    async fn test_memory_cache_expiration() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let key = "test_expiry";
        let quote = create_test_quote();
        
        // 设置一个短TTL
        cache.set(key, &quote, Duration::from_millis(10)).await.unwrap();
        
        // 立即获取应该成功
        assert!(cache.get(key).await.unwrap().is_some());
        
        // 等待过期
        tokio::time::sleep(Duration::from_millis(20)).await;
        
        // 过期后应该返回None
        assert!(cache.get(key).await.unwrap().is_none());
    }

    #[tokio::test]
    async fn test_memory_cache_ttl() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let key = "test_ttl";
        let quote = create_test_quote();
        
        // 设置缓存
        cache.set(key, &quote, Duration::from_secs(60)).await.unwrap();
        
        // 检查TTL
        let ttl = cache.ttl(key).await.unwrap();
        assert!(ttl.is_some());
        assert!(ttl.unwrap().as_secs() <= 60);
        
        // 更新TTL
        assert!(cache.expire(key, Duration::from_secs(30)).await.unwrap());
        let new_ttl = cache.ttl(key).await.unwrap();
        assert!(new_ttl.unwrap().as_secs() <= 30);
    }

    #[tokio::test]
    async fn test_memory_cache_batch_operations() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let quote1 = create_test_quote();
        let quote2 = create_test_quote();
        
        // 批量设置
        let items = vec![
            ("key1".to_string(), quote1.clone()),
            ("key2".to_string(), quote2.clone()),
        ];
        cache.mset(&items, Duration::from_secs(60)).await.unwrap();
        
        // 批量获取
        let keys = vec!["key1".to_string(), "key2".to_string(), "key3".to_string()];
        let results = cache.mget(&keys).await.unwrap();
        
        assert_eq!(results.len(), 3);
        assert!(results[0].is_some());
        assert!(results[1].is_some());
        assert!(results[2].is_none());
    }

    #[tokio::test]
    async fn test_memory_cache_stats() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let quote = create_test_quote();
        
        // 执行一些操作
        cache.set("key1", &quote, Duration::from_secs(60)).await.unwrap();
        cache.get("key1").await.unwrap();
        cache.get("nonexistent").await.unwrap();
        
        // 检查统计
        let stats = cache.stats().await.unwrap();
        assert!(stats.sets > 0);
        assert!(stats.hits > 0);
        assert!(stats.misses > 0);
        assert!(stats.hit_rate > 0.0 && stats.hit_rate < 1.0);
    }

    #[tokio::test]
    async fn test_memory_cache_health_check() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let is_healthy = cache.health_check().await.unwrap();
        assert!(is_healthy);
    }

    #[tokio::test]
    async fn test_memory_cache_cleanup() {
        let config = CacheConfig::default();
        let cache = MemoryPriceCache::new(config);
        
        let quote = create_test_quote();
        
        // 添加一些快速过期的项
        for i in 0..5 {
            cache.set(
                &format!("key{}", i), 
                &quote, 
                Duration::from_millis(1)
            ).await.unwrap();
        }
        
        // 等待过期
        tokio::time::sleep(Duration::from_millis(10)).await;
        
        // 清理
        let cleaned = cache.cleanup().await.unwrap();
        assert_eq!(cleaned, 5);
        
        // 确认缓存为空
        assert_eq!(cache.size().await.unwrap(), 0);
    }
}