//! Price Cache Module
//!
//! 价格缓存模块，提供多层缓存架构以优化价格计算性能

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::time::Duration;
use thiserror::Error;

use crate::types::{PriceQuote, PriceEngineResult, PriceEngineError};

pub mod memory;
pub mod redis;

pub use memory::MemoryPriceCache;
#[cfg(feature = "redis-cache")]
pub use redis::RedisPriceCache;

/// 缓存错误类型
#[derive(Error, Debug)]
pub enum CacheError {
    #[error("序列化错误: {0}")]
    Serialization(String),
    
    #[error("反序列化错误: {0}")]
    Deserialization(String),
    
    #[error("连接错误: {0}")]
    Connection(String),
    
    #[error("键不存在")]
    KeyNotFound,
    
    #[error("缓存已满")]
    CacheFull,
    
    #[error("过期的缓存项")]
    Expired,
    
    #[error("Redis错误: {0}")]
    Redis(String),
    
    #[error("内部错误: {0}")]
    Internal(String),
}

impl From<CacheError> for PriceEngineError {
    fn from(err: CacheError) -> Self {
        PriceEngineError::Cache(err.to_string())
    }
}

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    /// 默认过期时间（秒）
    pub default_ttl_seconds: u64,
    /// 最大缓存大小（内存缓存）
    pub max_size: usize,
    /// 清理间隔（秒）
    pub cleanup_interval_seconds: u64,
    /// 启用压缩（Redis缓存）
    pub enable_compression: bool,
    /// 键前缀
    pub key_prefix: String,
    /// 连接池大小（Redis缓存）
    pub pool_size: u32,
    /// 连接超时（毫秒）
    pub connection_timeout_ms: u64,
    /// 命令超时（毫秒）
    pub command_timeout_ms: u64,
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            default_ttl_seconds: 60,
            max_size: 10000,
            cleanup_interval_seconds: 300,
            enable_compression: true,
            key_prefix: "price_engine".to_string(),
            pool_size: 10,
            connection_timeout_ms: 5000,
            command_timeout_ms: 3000,
        }
    }
}

/// 缓存项元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheEntry<T> {
    /// 缓存的数据
    pub data: T,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 过期时间
    pub expires_at: DateTime<Utc>,
    /// 访问次数
    pub access_count: u64,
    /// 最后访问时间
    pub last_accessed: DateTime<Utc>,
    /// 数据版本
    pub version: u64,
}

impl<T> CacheEntry<T> {
    /// 创建新的缓存项
    pub fn new(data: T, ttl: Duration) -> Self {
        let now = Utc::now();
        Self {
            data,
            created_at: now,
            expires_at: now + chrono::Duration::from_std(ttl).unwrap_or(chrono::Duration::seconds(60)),
            access_count: 0,
            last_accessed: now,
            version: 1,
        }
    }

    /// 检查是否过期
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }

    /// 更新访问信息
    pub fn mark_accessed(&mut self) {
        self.access_count += 1;
        self.last_accessed = Utc::now();
    }

    /// 更新数据和版本
    pub fn update_data(&mut self, data: T, ttl: Duration) {
        self.data = data;
        self.version += 1;
        let now = Utc::now();
        self.last_accessed = now;
        self.expires_at = now + chrono::Duration::from_std(ttl).unwrap_or(chrono::Duration::seconds(60));
    }
}

/// 价格缓存接口
/// 
/// 所有缓存实现都需要实现这个trait
#[async_trait]
pub trait PriceCache: Send + Sync + std::fmt::Debug {
    /// 获取缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    /// 
    /// # 返回
    /// 返回缓存的价格报价，如果不存在或已过期则返回None
    async fn get(&self, key: &str) -> Result<Option<PriceQuote>, CacheError>;

    /// 设置缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    /// * `value` - 要缓存的价格报价
    /// * `ttl` - 生存时间
    async fn set(&self, key: &str, value: &PriceQuote, ttl: Duration) -> Result<(), CacheError>;

    /// 删除缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    async fn delete(&self, key: &str) -> Result<bool, CacheError>;

    /// 检查键是否存在
    /// 
    /// # 参数
    /// * `key` - 缓存键
    async fn exists(&self, key: &str) -> Result<bool, CacheError>;

    /// 设置过期时间
    /// 
    /// # 参数
    /// * `key` - 缓存键
    /// * `ttl` - 新的生存时间
    async fn expire(&self, key: &str, ttl: Duration) -> Result<bool, CacheError>;

    /// 获取剩余生存时间
    /// 
    /// # 参数
    /// * `key` - 缓存键
    async fn ttl(&self, key: &str) -> Result<Option<Duration>, CacheError>;

    /// 批量获取
    /// 
    /// # 参数
    /// * `keys` - 缓存键列表
    async fn mget(&self, keys: &[String]) -> Result<Vec<Option<PriceQuote>>, CacheError>;

    /// 批量设置
    /// 
    /// # 参数
    /// * `items` - 键值对列表
    /// * `ttl` - 生存时间
    async fn mset(&self, items: &[(String, PriceQuote)], ttl: Duration) -> Result<(), CacheError>;

    /// 清空缓存
    async fn clear(&self) -> Result<(), CacheError>;

    /// 获取缓存统计信息
    async fn stats(&self) -> Result<CacheStats, CacheError>;

    /// 获取缓存大小
    async fn size(&self) -> Result<usize, CacheError>;

    /// 健康检查
    async fn health_check(&self) -> Result<bool, CacheError>;

    /// 压缩清理过期项
    async fn cleanup(&self) -> Result<usize, CacheError>;
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// 命中次数
    pub hits: u64,
    /// 未命中次数
    pub misses: u64,
    /// 设置操作次数
    pub sets: u64,
    /// 删除操作次数
    pub deletes: u64,
    /// 过期项数量
    pub expired: u64,
    /// 当前大小
    pub current_size: usize,
    /// 最大大小
    pub max_size: usize,
    /// 内存使用量（字节）
    pub memory_usage: usize,
    /// 命中率
    pub hit_rate: f64,
    /// 平均访问时间（纳秒）
    pub average_access_time_ns: u64,
    /// 统计开始时间
    pub stats_start: DateTime<Utc>,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl CacheStats {
    /// 创建新的统计信息
    pub fn new() -> Self {
        let now = Utc::now();
        Self {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            expired: 0,
            current_size: 0,
            max_size: 0,
            memory_usage: 0,
            hit_rate: 0.0,
            average_access_time_ns: 0,
            stats_start: now,
            last_updated: now,
        }
    }

    /// 更新命中率
    pub fn update_hit_rate(&mut self) {
        let total = self.hits + self.misses;
        self.hit_rate = if total > 0 {
            self.hits as f64 / total as f64
        } else {
            0.0
        };
        self.last_updated = Utc::now();
    }

    /// 记录命中
    pub fn record_hit(&mut self) {
        self.hits += 1;
        self.update_hit_rate();
    }

    /// 记录未命中
    pub fn record_miss(&mut self) {
        self.misses += 1;
        self.update_hit_rate();
    }

    /// 记录设置操作
    pub fn record_set(&mut self) {
        self.sets += 1;
        self.last_updated = Utc::now();
    }

    /// 记录删除操作
    pub fn record_delete(&mut self) {
        self.deletes += 1;
        self.last_updated = Utc::now();
    }

    /// 记录过期项
    pub fn record_expired(&mut self) {
        self.expired += 1;
        self.last_updated = Utc::now();
    }

    /// 重置统计
    pub fn reset(&mut self) {
        *self = Self::new();
    }
}

impl Default for CacheStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 缓存键构建器
pub struct CacheKeyBuilder {
    prefix: String,
}

impl CacheKeyBuilder {
    /// 创建新的键构建器
    pub fn new(prefix: &str) -> Self {
        Self {
            prefix: prefix.to_string(),
        }
    }

    /// 构建价格缓存键
    pub fn price_key(&self, token_a: &str, token_b: &str, amount: Option<u64>) -> String {
        if let Some(amt) = amount {
            format!("{}:price:{}:{}:{}", self.prefix, token_a, token_b, amt)
        } else {
            format!("{}:price:{}:{}", self.prefix, token_a, token_b)
        }
    }

    /// 构建流动性深度键
    pub fn liquidity_key(&self, token_a: &str, token_b: &str, range: f64) -> String {
        format!("{}:liquidity:{}:{}:{}", self.prefix, token_a, token_b, range)
    }

    /// 构建交换估算键
    pub fn swap_key(&self, input_token: &str, output_token: &str, amount: u64, dex: &str) -> String {
        format!("{}:swap:{}:{}:{}:{}", self.prefix, input_token, output_token, amount, dex)
    }

    /// 构建质量评分键
    pub fn quality_key(&self, token_a: &str, token_b: &str) -> String {
        format!("{}:quality:{}:{}", self.prefix, token_a, token_b)
    }

    /// 构建健康状态键
    pub fn health_key(&self, dex: &str) -> String {
        format!("{}:health:{}", self.prefix, dex)
    }

    /// 构建统计键
    pub fn stats_key(&self, category: &str) -> String {
        format!("{}:stats:{}", self.prefix, category)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cache_entry_creation() {
        let data = "test_data".to_string();
        let ttl = Duration::from_secs(60);
        let entry = CacheEntry::new(data.clone(), ttl);

        assert_eq!(entry.data, data);
        assert_eq!(entry.access_count, 0);
        assert_eq!(entry.version, 1);
        assert!(!entry.is_expired());
    }

    #[test]
    fn test_cache_entry_expiration() {
        let data = "test_data".to_string();
        let ttl = Duration::from_millis(1);
        let mut entry = CacheEntry::new(data, ttl);

        std::thread::sleep(Duration::from_millis(10));
        assert!(entry.is_expired());

        // 测试访问标记
        entry.mark_accessed();
        assert_eq!(entry.access_count, 1);
    }

    #[test]
    fn test_cache_stats() {
        let mut stats = CacheStats::new();
        
        stats.record_hit();
        stats.record_miss();
        stats.record_set();
        
        assert_eq!(stats.hits, 1);
        assert_eq!(stats.misses, 1);
        assert_eq!(stats.sets, 1);
        assert_eq!(stats.hit_rate, 0.5);
    }

    #[test]
    fn test_cache_key_builder() {
        let builder = CacheKeyBuilder::new("test_prefix");
        
        let price_key = builder.price_key("SOL", "USDC", Some(1000));
        assert_eq!(price_key, "test_prefix:price:SOL:USDC:1000");
        
        let liquidity_key = builder.liquidity_key("SOL", "USDC", 10.0);
        assert_eq!(liquidity_key, "test_prefix:liquidity:SOL:USDC:10");
        
        let swap_key = builder.swap_key("SOL", "USDC", 1000, "raydium");
        assert_eq!(swap_key, "test_prefix:swap:SOL:USDC:1000:raydium");
    }

    #[test]
    fn test_cache_config_default() {
        let config = CacheConfig::default();
        assert_eq!(config.default_ttl_seconds, 60);
        assert_eq!(config.max_size, 10000);
        assert!(config.enable_compression);
    }
}