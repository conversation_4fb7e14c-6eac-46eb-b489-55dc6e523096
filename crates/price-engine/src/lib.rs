//! Price Engine Crate
//!
//! 实时价格计算引擎，为DEX套利系统提供统一的价格计算和同步功能
//!
//! ## 核心功能
//!
//! - **统一价格计算**: 支持Raydium CLMM、Meteora DLMM等多个DEX的价格计算
//! - **实时价格同步**: 基于池状态变化的实时价格更新
//! - **智能缓存**: 多层缓存架构，支持内存和Redis缓存
//! - **质量监控**: 价格质量评估和异常检测
//! - **性能优化**: 并发计算，延迟<10ms，精度误差<0.1%
//!
//! ## 架构概览
//!
//! ```text
//! ┌─────────────────────────────────────────────────────────────┐
//! │                    Price Engine                              │
//! ├─────────────────┬─────────────────┬─────────────────────────┤
//! │   Calculator    │    Service      │       Integration       │
//! │                 │                 │                         │
//! │ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────────┐ │
//! │ │ Raydium     │ │ │ Price       │ │ │ Data Flow           │ │
//! │ │ CLMM        │ │ │ Service     │ │ │ Manager             │ │
//! │ └─────────────┘ │ └─────────────┘ │ └─────────────────────┘ │
//! │                 │                 │                         │
//! │ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────────┐ │
//! │ │ Meteora     │ │ │ Sync        │ │ │ Event Listener      │ │
//! │ │ DLMM        │ │ │ Manager     │ │ │                     │ │
//! │ │             │ │ │             │ │ │                     │ │
//! │ └─────────────┘ │ └─────────────┘ │ └─────────────────────┘ │
//! └─────────────────┴─────────────────┴─────────────────────────┘
//! ┌─────────────────┬─────────────────┬─────────────────────────┐
//! │     Cache       │    Quality      │       Types             │
//! │                 │                 │                         │
//! │ ┌─────────────┐ │ ┌─────────────┐ │ ┌─────────────────────┐ │
//! │ │ Memory      │ │ │ Monitor     │ │ │ Core Types          │ │
//! │ │ Cache       │ │ │             │ │ │                     │ │
//! │ └─────────────┘ │ └─────────────┘ │ └─────────────────────┘ │
//! │                 │                 │                         │
//! │ ┌─────────────┐ │ ┌─────────────┐ │                         │
//! │ │ Redis       │ │ │ Validator   │ │                         │
//! │ │ Cache       │ │ │             │ │                         │
//! │ └─────────────┘ │ └─────────────┘ │                         │
//! └─────────────────┴─────────────────┴─────────────────────────┘
//! ```
//!
//! ## 使用示例
//!
//! ### 基本价格查询
//!
//! ```rust,no_run
//! use price_engine::{PriceCalculationService, TokenPair, MemoryPriceCache, PriceQualityMonitor};
//! use price_engine::cache::CacheConfig;
//! use solana_sdk::pubkey::Pubkey;
//! use std::str::FromStr;
//! use std::sync::Arc;
//! use tokio::sync::RwLock;
//!
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! // 创建价格服务
//! let cache = Arc::new(MemoryPriceCache::new(CacheConfig::default()));
//! let monitor = Arc::new(RwLock::new(PriceQualityMonitor::new()));
//! let service = PriceCalculationService::new(cache, monitor, None);
//!
//! // 创建代币对
//! let token_pair = TokenPair::new(
//!     Pubkey::from_str("So11111111111111111111111111111111111111112")?, // SOL
//!     Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?, // USDC
//! );
//!
//! // 获取最佳价格
//! let quote = service.get_best_price(&token_pair, 1_000_000).await?;
//! println!("最佳价格: {} USDC/SOL", quote.spot_price);
//! # Ok(())
//! # }
//! ```
//!
//! ### 交换估算
//!
//! ```rust,no_run
//! # async fn example() -> Result<(), Box<dyn std::error::Error>> {
//! # use price_engine::{PriceCalculationService, MemoryPriceCache, PriceQualityMonitor};
//! # use price_engine::cache::CacheConfig;
//! # use solana_sdk::pubkey::Pubkey;
//! # use std::str::FromStr;
//! # use std::sync::Arc;
//! # use tokio::sync::RwLock;
//! # let cache = Arc::new(MemoryPriceCache::new(CacheConfig::default()));
//! # let monitor = Arc::new(RwLock::new(PriceQualityMonitor::new()));
//! # let service = PriceCalculationService::new(cache, monitor, None);
//! let sol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112")?;
//! let usdc_mint = Pubkey::from_str("EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v")?;
//!
//! // 估算交换1 SOL能得到多少USDC
//! let estimation = service.estimate_swap(
//!     1_000_000_000, // 1 SOL (9 decimals)
//!     &sol_mint,
//!     &usdc_mint,
//!     None, // 自动选择最佳DEX
//! ).await?;
//!
//! println!("输出: {} USDC", estimation.output_amount);
//! println!("费用: {} lamports", estimation.fee_amount);
//! println!("价格影响: {:.2}%", estimation.price_impact_percent);
//! # Ok(())
//! # }
//! ```

// 核心模块导出
pub mod types;
pub mod calculator;
pub mod cache;
pub mod quality;
pub mod service;
pub mod integration;

// 重新导出核心类型和功能
pub use types::*;
pub use calculator::{PriceCalculator, RaydiumClmmPriceCalculator, MeteoraeDlmmPriceCalculator};
pub use cache::{PriceCache, MemoryPriceCache};
pub use quality::{PriceQualityMonitor, PriceValidator};
pub use service::{PriceCalculationService, PriceSyncManager};
pub use integration::PriceDataFlowManager;

// 便利重新导出
pub use calculator::traits::{
    PriceAggregator, PriceListener, PoolInfo, HealthStatus,
    AggregatedPrice, AggregationStats, PriceSpreadAnalysis, ArbitrageOpportunity,
    ListenerStatus, ValidationResult, AnomalyResult, AnomalyType
};

// 配置和便利构造函数
use std::sync::Arc;
use tokio::sync::RwLock;
// 重复导入已在上面的 pub use 语句中处理

/// 默认价格引擎构建器
/// 
/// 提供便利的方法来配置和创建价格计算服务
pub struct PriceEngineBuilder {
    config: Option<PriceCalculationConfig>,
    cache_config: Option<cache::CacheConfig>,
    enable_redis: bool,
    redis_url: Option<String>,
}

impl PriceEngineBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: None,
            cache_config: None,
            enable_redis: false,
            redis_url: None,
        }
    }

    /// 设置价格计算配置
    pub fn with_config(mut self, config: PriceCalculationConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// 设置缓存配置
    pub fn with_cache_config(mut self, cache_config: cache::CacheConfig) -> Self {
        self.cache_config = Some(cache_config);
        self
    }

    /// 启用Redis缓存
    #[cfg(feature = "redis-cache")]
    pub fn with_redis(mut self, redis_url: String) -> Self {
        self.enable_redis = true;
        self.redis_url = Some(redis_url);
        self
    }

    /// 构建价格计算服务
    pub async fn build(self) -> PriceEngineResult<PriceCalculationService> {
        let config = self.config.unwrap_or_default();
        let cache_config = self.cache_config.unwrap_or_default();

        // 创建缓存
        let cache: Arc<dyn PriceCache> = if self.enable_redis {
            #[cfg(feature = "redis-cache")]
            {
                Arc::new(
                    cache::RedisPriceCache::new(
                        &self.redis_url.expect("Redis URL is required"),
                        cache_config,
                    ).await
                    .map_err(|e| PriceEngineError::Config(format!("Redis缓存初始化失败: {}", e)))?
                )
            }
            #[cfg(not(feature = "redis-cache"))]
            {
                return Err(PriceEngineError::Config(
                    "Redis缓存功能未启用，请使用redis-cache feature".to_string()
                ));
            }
        } else {
            Arc::new(MemoryPriceCache::new(cache_config))
        };

        // 创建质量监控器
        let quality_monitor = Arc::new(RwLock::new(PriceQualityMonitor::new()));

        // 创建价格计算服务
        Ok(PriceCalculationService::new(cache, quality_monitor, Some(config)))
    }
}

impl Default for PriceEngineBuilder {
    fn default() -> Self {
        Self::new()
    }
}

/// 便利函数：创建默认的价格引擎
/// 
/// 使用内存缓存和默认配置
pub async fn create_default_price_engine() -> PriceEngineResult<PriceCalculationService> {
    PriceEngineBuilder::new().build().await
}

/// 便利函数：创建带Redis缓存的价格引擎
#[cfg(feature = "redis-cache")]
pub async fn create_redis_price_engine(redis_url: &str) -> PriceEngineResult<PriceCalculationService> {
    PriceEngineBuilder::new()
        .with_redis(redis_url.to_string())
        .build()
        .await
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_default_price_engine_creation() {
        let engine = create_default_price_engine().await;
        assert!(engine.is_ok());
    }

    #[tokio::test] 
    async fn test_price_engine_builder() {
        let config = PriceCalculationConfig::default();
        let engine = PriceEngineBuilder::new()
            .with_config(config)
            .build()
            .await;
        assert!(engine.is_ok());
    }

    #[test]
    fn test_builder_default() {
        let builder = PriceEngineBuilder::default();
        assert!(builder.config.is_none());
        assert!(!builder.enable_redis);
    }
}