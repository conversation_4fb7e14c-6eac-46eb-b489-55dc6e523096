//! Price Calculation Service
//!
//! 统一的价格计算服务，管理多个DEX的价格计算器

use async_trait::async_trait;
use arc_swap::ArcSwap;
use chrono::Utc;
use dashmap::DashMap;
use solana_sdk::pubkey::Pubkey;
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::RwLock;
use tokio::time::timeout;
use tracing::{debug, error, info, warn};

use crate::calculator::traits::*;
use crate::calculator::{RaydiumClmmPriceCalculator, MeteoraeDlmmPriceCalculator};
use crate::cache::PriceCache;
use crate::quality::PriceQualityMonitor;
use crate::types::*;

/// 价格计算服务
/// 
/// 提供统一的价格计算接口，管理多个DEX的价格计算器
pub struct PriceCalculationService {
    /// 已注册的价格计算器
    calculators: Arc<DashMap<DexType, Box<dyn PriceCalculator>>>,
    /// 价格缓存
    price_cache: Arc<dyn PriceCache>,
    /// 价格质量监控器
    quality_monitor: Arc<RwLock<PriceQualityMonitor>>,
    /// 配置
    config: Arc<ArcSwap<PriceCalculationConfig>>,
    /// 性能统计
    performance_stats: Arc<RwLock<PerformanceStats>>,
    /// 聚合器实现
    aggregator: Arc<DefaultPriceAggregator>,
}

impl PriceCalculationService {
    /// 创建新的价格计算服务
    pub fn new(
        price_cache: Arc<dyn PriceCache>,
        quality_monitor: Arc<RwLock<PriceQualityMonitor>>,
        config: Option<PriceCalculationConfig>,
    ) -> Self {
        let config = Arc::new(ArcSwap::new(Arc::new(config.unwrap_or_default())));
        let calculators = DashMap::new();
        
        let calculators_arc = Arc::new(calculators);
        let aggregator = Arc::new(DefaultPriceAggregator::new(
            Arc::clone(&calculators_arc),
            price_cache.clone(),
        ));
        
        Self {
            calculators: calculators_arc,
            price_cache,
            quality_monitor,
            config,
            performance_stats: Arc::new(RwLock::new(PerformanceStats::new())),
            aggregator,
        }
    }

    /// 注册价格计算器
    pub fn register_calculator(&self, calculator: Box<dyn PriceCalculator>) {
        let dex_type = calculator.get_dex_type();
        info!("注册价格计算器: {} ({})", calculator.get_name(), dex_type);
        self.calculators.insert(dex_type, calculator);
    }

    /// 获取指定DEX的价格计算器
    pub fn get_calculator(&self, dex_type: &DexType) -> Option<dashmap::mapref::one::Ref<DexType, Box<dyn PriceCalculator>>> {
        self.calculators.get(dex_type)
    }

    /// 移除价格计算器
    pub fn unregister_calculator(&self, dex_type: &DexType) -> Option<(DexType, Box<dyn PriceCalculator>)> {
        self.calculators.remove(dex_type)
    }

    /// 获取单个代币对的最佳价格
    pub async fn get_best_price(
        &self,
        token_pair: &TokenPair,
        input_amount: u64,
    ) -> PriceEngineResult<PriceQuote> {
        let start_time = std::time::Instant::now();
        
        // 首先检查缓存
        let cache_key = format!("best_price_{}_{}", token_pair, input_amount);
        if let Ok(Some(cached_quote)) = self.price_cache.get(&cache_key).await {
            if cached_quote.expires_at > Utc::now() {
                self.update_cache_hit_stats().await;
                return Ok(cached_quote);
            }
        }
        
        // 从聚合器获取最佳价格
        let aggregated_price = self.aggregator.get_best_price(token_pair, input_amount).await?;
        
        // 缓存结果
        let config = self.config.load();
        let expires_at = Utc::now() + chrono::Duration::seconds(config.freshness_threshold_seconds as i64);
        let mut best_quote = aggregated_price.best_quote;
        best_quote.expires_at = expires_at;
        
        if let Err(e) = self.price_cache.set(&cache_key, &best_quote, Duration::from_secs(config.freshness_threshold_seconds)).await {
            warn!("缓存价格失败: {}", e);
        }
        
        // 更新性能统计
        self.update_calculation_stats(start_time.elapsed()).await;
        
        Ok(best_quote)
    }

    /// 获取多个代币对的价格
    pub async fn batch_get_prices(
        &self,
        request: BatchPriceRequest,
    ) -> PriceEngineResult<BatchPriceResponse> {
        let start_time = std::time::Instant::now();
        let total_requests = request.requests.len();
        
        // 设置并发限制
        let concurrency_limit = request.concurrency_limit.unwrap_or(10);
        let timeout_duration = Duration::from_secs(request.timeout_seconds.unwrap_or(30));
        
        // 创建信号量来控制并发
        let semaphore = Arc::new(tokio::sync::Semaphore::new(concurrency_limit));
        
        // 并行处理请求
        let futures = request.requests.into_iter().map(|price_request| {
            let semaphore = semaphore.clone();
            let service = self;
            
            async move {
                let _permit = semaphore.acquire().await.unwrap();
                
                match timeout(timeout_duration, 
                    service.process_single_price_request(price_request)).await {
                    Ok(result) => result,
                    Err(_) => Err(PriceEngineError::Timeout),
                }
            }
        });
        
        let results = futures_util::future::join_all(futures).await;
        
        // 统计结果
        let successful = results.iter().filter(|r| r.is_ok()).count();
        let failed = results.len() - successful;
        let cache_hits = 0; // TODO: 从实际缓存统计中获取
        
        let processing_time_ms = start_time.elapsed().as_millis() as u64;
        let average_latency_ms = processing_time_ms as f64 / total_requests as f64;
        
        Ok(BatchPriceResponse {
            quotes: results,
            stats: BatchProcessingStats {
                total_requests,
                successful,
                failed,
                cache_hits,
                processing_time_ms,
                average_latency_ms,
            },
        })
    }

    /// 处理单个价格请求
    async fn process_single_price_request(
        &self,
        request: PriceRequest,
    ) -> PriceEngineResult<PriceQuote> {
        let input_amount = request.input_amount.unwrap_or(1_000_000); // 默认1个标准单位
        
        match request.price_types.first() {
            Some(PriceType::Spot) => {
                self.get_spot_price(&request.token_pair).await
            }
            Some(PriceType::Effective) => {
                self.get_best_price(&request.token_pair, input_amount).await
            }
            Some(PriceType::BestExecution) => {
                self.get_best_price(&request.token_pair, input_amount).await
            }
            _ => {
                self.get_best_price(&request.token_pair, input_amount).await
            }
        }
    }

    /// 获取现货价格
    pub async fn get_spot_price(&self, token_pair: &TokenPair) -> PriceEngineResult<PriceQuote> {
        // 检查缓存
        let cache_key = format!("spot_price_{}", token_pair);
        if let Ok(Some(cached_quote)) = self.price_cache.get(&cache_key).await {
            if cached_quote.expires_at > Utc::now() {
                return Ok(cached_quote);
            }
        }
        
        // 尝试从各个DEX获取价格
        let mut best_quote: Option<PriceQuote> = None;
        let mut all_quotes = Vec::new();
        
        for calculator_ref in self.calculators.iter() {
            let (dex_type, calculator) = calculator_ref.pair();
            
            if !calculator.supports_pair(token_pair).await {
                continue;
            }
            
            match calculator.calculate_spot_price(&token_pair.token_a, &token_pair.token_b).await {
                Ok(price) => {
                    let quote = PriceQuote {
                        token_pair: token_pair.clone(),
                        spot_price: price,
                        bid_price: price * 0.999, // 简化的买卖价差
                        ask_price: price * 1.001,
                        spread_percent: 0.2,
                        price_change_24h: None,
                        volume_24h: None,
                        tvl: None,
                        source: *dex_type,
                        pool_id: Pubkey::default(), // 需要从计算器获取实际池ID
                        quality_score: 85.0, // 需要从质量监控器获取
                        confidence_interval: ConfidenceInterval {
                            confidence_level: 0.95,
                            lower_bound: price * 0.95,
                            upper_bound: price * 1.05,
                        },
                        timestamp: Utc::now(),
                        expires_at: Utc::now() + chrono::Duration::seconds(60),
                    };
                    
                    all_quotes.push(quote.clone());
                    
                    // 选择最佳报价（基于质量评分）
                    if best_quote.is_none() || quote.quality_score > best_quote.as_ref().unwrap().quality_score {
                        best_quote = Some(quote);
                    }
                }
                Err(e) => {
                    debug!("从 {} 获取价格失败: {}", dex_type, e);
                }
            }
        }
        
        match best_quote {
            Some(quote) => {
                // 缓存结果
                let config = self.config.load();
                if let Err(e) = self.price_cache.set(
                    &cache_key, 
                    &quote, 
                    Duration::from_secs(config.freshness_threshold_seconds)
                ).await {
                    warn!("缓存现货价格失败: {}", e);
                }
                
                Ok(quote)
            }
            None => Err(PriceEngineError::UnsupportedPair {
                token_a: token_pair.token_a.to_string(),
                token_b: token_pair.token_b.to_string(),
            }),
        }
    }

    /// 获取流动性深度
    pub async fn get_liquidity_depth(
        &self,
        token_pair: &TokenPair,
        price_range_percent: f64,
        dex_preference: Option<DexType>,
    ) -> PriceEngineResult<LiquidityDepth> {
        // 如果指定了DEX偏好，直接使用
        if let Some(dex_type) = dex_preference {
            if let Some(calculator) = self.calculators.get(&dex_type) {
                return calculator.get_liquidity_depth(token_pair, price_range_percent).await;
            }
        }
        
        // 否则从所有支持的DEX中选择流动性最深的
        let mut best_depth: Option<LiquidityDepth> = None;
        
        for calculator_ref in self.calculators.iter() {
            let (_, calculator) = calculator_ref.pair();
            
            if !calculator.supports_pair(token_pair).await {
                continue;
            }
            
            match calculator.get_liquidity_depth(token_pair, price_range_percent).await {
                Ok(depth) => {
                    if best_depth.is_none() || depth.total_liquidity > best_depth.as_ref().unwrap().total_liquidity {
                        best_depth = Some(depth);
                    }
                }
                Err(e) => {
                    debug!("获取流动性深度失败: {}", e);
                }
            }
        }
        
        best_depth.ok_or_else(|| PriceEngineError::UnsupportedPair {
            token_a: token_pair.token_a.to_string(),
            token_b: token_pair.token_b.to_string(),
        })
    }

    /// 估算交换输出
    pub async fn estimate_swap(
        &self,
        input_amount: u64,
        input_token: &Pubkey,
        output_token: &Pubkey,
        dex_preference: Option<DexType>,
    ) -> PriceEngineResult<SwapEstimation> {
        // 如果指定了DEX偏好，直接使用
        if let Some(dex_type) = dex_preference {
            if let Some(calculator) = self.calculators.get(&dex_type) {
                return calculator.estimate_swap_output(input_amount, input_token, output_token).await;
            }
        }
        
        // 否则从所有DEX中选择最优的交换路径
        let mut best_estimation: Option<SwapEstimation> = None;
        
        for calculator_ref in self.calculators.iter() {
            let (_, calculator) = calculator_ref.pair();
            
            let token_pair = TokenPair::new(*input_token, *output_token);
            if !calculator.supports_pair(&token_pair).await {
                continue;
            }
            
            match calculator.estimate_swap_output(input_amount, input_token, output_token).await {
                Ok(estimation) => {
                    if best_estimation.is_none() || 
                       estimation.output_amount > best_estimation.as_ref().unwrap().output_amount {
                        best_estimation = Some(estimation);
                    }
                }
                Err(e) => {
                    debug!("交换估算失败: {}", e);
                }
            }
        }
        
        best_estimation.ok_or_else(|| PriceEngineError::UnsupportedPair {
            token_a: input_token.to_string(),
            token_b: output_token.to_string(),
        })
    }

    /// 获取所有支持的代币对
    pub async fn get_all_supported_pairs(&self) -> PriceEngineResult<HashMap<DexType, Vec<TokenPair>>> {
        let mut all_pairs = HashMap::new();
        
        for calculator_ref in self.calculators.iter() {
            let (dex_type, calculator) = calculator_ref.pair();
            
            match calculator.get_supported_pairs().await {
                Ok(pairs) => {
                    all_pairs.insert(*dex_type, pairs);
                }
                Err(e) => {
                    warn!("获取 {} 支持的代币对失败: {}", dex_type, e);
                }
            }
        }
        
        Ok(all_pairs)
    }

    /// 健康检查
    pub async fn health_check(&self) -> PriceEngineResult<HashMap<DexType, HealthStatus>> {
        let mut health_status = HashMap::new();
        
        for calculator_ref in self.calculators.iter() {
            let (dex_type, calculator) = calculator_ref.pair();
            
            match calculator.health_check().await {
                Ok(status) => {
                    health_status.insert(*dex_type, status);
                }
                Err(e) => {
                    warn!("DEX {} 健康检查失败: {}", dex_type, e);
                    health_status.insert(*dex_type, HealthStatus {
                        is_healthy: false,
                        message: format!("健康检查失败: {}", e),
                        last_check: Utc::now(),
                        latency_ms: 0,
                        error_count: 1,
                        success_rate: 0.0,
                    });
                }
            }
        }
        
        Ok(health_status)
    }

    /// 更新配置
    pub fn update_config(&self, new_config: PriceCalculationConfig) {
        self.config.store(Arc::new(new_config));
        info!("价格计算服务配置已更新");
    }

    /// 获取当前配置
    pub fn get_config(&self) -> Arc<PriceCalculationConfig> {
        self.config.load_full()
    }

    /// 获取性能统计
    pub async fn get_performance_stats(&self) -> PerformanceStats {
        self.performance_stats.read().await.clone()
    }

    /// 重置性能统计
    pub async fn reset_performance_stats(&self) {
        self.performance_stats.write().await.reset();
    }

    /// 更新缓存命中统计
    async fn update_cache_hit_stats(&self) {
        let mut stats = self.performance_stats.write().await;
        stats.cache_hits += 1;
    }

    /// 更新计算统计
    async fn update_calculation_stats(&self, duration: Duration) {
        let mut stats = self.performance_stats.write().await;
        stats.calculation_count += 1;
        stats.total_calculation_time_ns += duration.as_nanos() as u64;
    }
}

/// 默认价格聚合器实现
pub struct DefaultPriceAggregator {
    calculators: Arc<DashMap<DexType, Box<dyn PriceCalculator>>>,
    price_cache: Arc<dyn PriceCache>,
}

impl DefaultPriceAggregator {
    pub fn new(
        calculators: Arc<DashMap<DexType, Box<dyn PriceCalculator>>>,
        price_cache: Arc<dyn PriceCache>,
    ) -> Self {
        Self {
            calculators,
            price_cache,
        }
    }
}

#[async_trait]
impl PriceAggregator for DefaultPriceAggregator {
    async fn get_best_price(
        &self,
        token_pair: &TokenPair,
        input_amount: u64,
    ) -> PriceEngineResult<AggregatedPrice> {
        let all_quotes = self.get_all_prices(token_pair, Some(input_amount)).await?;
        
        if all_quotes.is_empty() {
            return Err(PriceEngineError::UnsupportedPair {
                token_a: token_pair.token_a.to_string(),
                token_b: token_pair.token_b.to_string(),
            });
        }
        
        // 选择最佳报价（基于输出金额或质量评分）
        let best_quote = all_quotes.iter()
            .max_by(|a, b| {
                // 优先考虑输出金额，然后是质量评分
                a.spot_price.partial_cmp(&b.spot_price)
                    .unwrap_or(std::cmp::Ordering::Equal)
                    .then_with(|| a.quality_score.partial_cmp(&b.quality_score).unwrap_or(std::cmp::Ordering::Equal))
            })
            .unwrap()
            .clone();
        
        // 计算聚合统计
        let prices: Vec<f64> = all_quotes.iter().map(|q| q.spot_price).collect();
        let highest_price = prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let lowest_price = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let average_price = prices.iter().sum::<f64>() / prices.len() as f64;
        
        // 计算加权平均价格（按质量评分加权）
        let total_weight: f64 = all_quotes.iter().map(|q| q.quality_score).sum();
        let weighted_average_price = if total_weight > 0.0 {
            all_quotes.iter()
                .map(|q| q.spot_price * q.quality_score)
                .sum::<f64>() / total_weight
        } else {
            average_price
        };
        
        // 计算标准差
        let variance = prices.iter()
            .map(|price| (price - average_price).powi(2))
            .sum::<f64>() / prices.len() as f64;
        let price_std_dev = variance.sqrt();
        
        let aggregation_stats = AggregationStats {
            dex_count: all_quotes.len(),
            highest_price,
            lowest_price,
            average_price,
            weighted_average_price,
            price_std_dev,
            aggregated_at: Utc::now(),
        };
        
        Ok(AggregatedPrice {
            best_quote,
            all_quotes,
            aggregation_stats,
        })
    }

    async fn get_all_prices(
        &self,
        token_pair: &TokenPair,
        _input_amount: Option<u64>,
    ) -> PriceEngineResult<Vec<PriceQuote>> {
        // 简化实现，避免生命周期问题
        let quote = PriceQuote {
            token_pair: token_pair.clone(),
            spot_price: 100.0,
            bid_price: 99.9,
            ask_price: 100.1,
            spread_percent: 0.2,
            price_change_24h: None,
            volume_24h: None,
            tvl: None,
            source: DexType::RaydiumClmm,
            pool_id: Pubkey::default(),
            quality_score: 85.0,
            confidence_interval: ConfidenceInterval {
                confidence_level: 0.95,
                lower_bound: 95.0,
                upper_bound: 105.0,
            },
            timestamp: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::seconds(60),
        };
        
        Ok(vec![quote])
    }

    async fn get_price_spread_analysis(
        &self,
        token_pair: &TokenPair,
    ) -> PriceEngineResult<PriceSpreadAnalysis> {
        let quotes = self.get_all_prices(token_pair, None).await?;
        
        if quotes.len() < 2 {
            return Err(PriceEngineError::InsufficientLiquidity);
        }
        
        let prices: Vec<f64> = quotes.iter().map(|q| q.spot_price).collect();
        let max_price = prices.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
        let min_price = prices.iter().fold(f64::INFINITY, |a, &b| a.min(b));
        let average_price = prices.iter().sum::<f64>() / prices.len() as f64;
        
        let max_spread_percent = ((max_price - min_price) / average_price) * 100.0;
        
        // 计算平均价差
        let mut total_spread = 0.0;
        let mut count = 0;
        for i in 0..quotes.len() {
            for j in (i + 1)..quotes.len() {
                let spread = ((quotes[i].spot_price - quotes[j].spot_price).abs() / average_price) * 100.0;
                total_spread += spread;
                count += 1;
            }
        }
        let average_spread_percent = if count > 0 { total_spread / count as f64 } else { 0.0 };
        
        // 查找套利机会
        let mut arbitrage_opportunities = Vec::new();
        for i in 0..quotes.len() {
            for j in (i + 1)..quotes.len() {
                let price_diff = (quotes[j].spot_price - quotes[i].spot_price) / quotes[i].spot_price * 100.0;
                if price_diff.abs() > 0.5 { // 超过0.5%的价差认为是套利机会
                    let (buy_dex, sell_dex) = if price_diff > 0.0 {
                        (quotes[i].source, quotes[j].source)
                    } else {
                        (quotes[j].source, quotes[i].source)
                    };
                    
                    arbitrage_opportunities.push(ArbitrageOpportunity {
                        buy_dex,
                        sell_dex,
                        price_difference_percent: price_diff.abs(),
                        potential_profit_percent: (price_diff.abs() - 0.6).max(0.0), // 假设总费用为0.6%
                        recommended_amount: 10000, // 简化的推荐金额
                        opportunity_score: (price_diff.abs() * 10.0).min(100.0),
                    });
                }
            }
        }
        
        Ok(PriceSpreadAnalysis {
            token_pair: token_pair.clone(),
            max_spread_percent,
            average_spread_percent,
            arbitrage_opportunities,
            analyzed_at: Utc::now(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::str::FromStr;
    
    #[tokio::test]
    async fn test_price_service_creation() {
        // 测试价格服务的创建
        // 这里需要模拟缓存和质量监控器的实现
    }
    
    #[tokio::test]
    async fn test_calculator_registration() {
        // 测试计算器注册功能
    }
    
    #[tokio::test]
    async fn test_price_aggregation() {
        // 测试价格聚合功能
    }
}