//! Integration Module
//!
//! 集成模块，负责将价格引擎与其他系统组件集成

pub mod data_flow;

pub use data_flow::PriceDataFlowManager;

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use thiserror::Error;

use crate::types::{DexType, PriceQuote, TokenPair, PriceUpdateEvent, PriceUpdateTrigger};

/// 集成错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum IntegrationError {
    #[error("数据流错误: {0}")]
    DataFlow(String),
    
    #[error("事件处理错误: {0}")]
    EventHandling(String),
    
    #[error("状态同步错误: {0}")]
    StateSync(String),
    
    #[error("连接错误: {0}")]
    Connection(String),
    
    #[error("配置错误: {0}")]
    Configuration(String),
    
    #[error("超时错误: {message}, 耗时: {elapsed_ms}ms")]
    Timeout { message: String, elapsed_ms: u64 },
    
    #[error("协议错误: {0}")]
    Protocol(String),
    
    #[error("内部错误: {0}")]
    Internal(String),
}

/// 集成配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationConfig {
    /// 启用的集成模块
    pub enabled_modules: Vec<IntegrationModule>,
    /// 事件缓冲区大小
    pub event_buffer_size: usize,
    /// 批处理大小
    pub batch_size: usize,
    /// 批处理超时（毫秒）
    pub batch_timeout_ms: u64,
    /// 重试配置
    pub retry_config: RetryConfig,
    /// 健康检查间隔（秒）
    pub health_check_interval_seconds: u64,
    /// 指标收集间隔（秒）
    pub metrics_interval_seconds: u64,
}

impl Default for IntegrationConfig {
    fn default() -> Self {
        Self {
            enabled_modules: vec![
                IntegrationModule::StateManager,
                IntegrationModule::DataParser,
                IntegrationModule::PriceCache,
            ],
            event_buffer_size: 1000,
            batch_size: 50,
            batch_timeout_ms: 100,
            retry_config: RetryConfig::default(),
            health_check_interval_seconds: 30,
            metrics_interval_seconds: 60,
        }
    }
}

/// 集成模块类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum IntegrationModule {
    /// 状态管理器集成
    StateManager,
    /// 数据解析器集成
    DataParser,
    /// 价格缓存集成
    PriceCache,
    /// 质量监控集成
    QualityMonitor,
    /// 消息总线集成
    MessageBus,
    /// 外部API集成
    ExternalApi,
    /// 数据库集成
    Database,
    /// 监控系统集成
    Monitoring,
}

/// 重试配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryConfig {
    /// 最大重试次数
    pub max_retries: u32,
    /// 初始延迟（毫秒）
    pub initial_delay_ms: u64,
    /// 最大延迟（毫秒）
    pub max_delay_ms: u64,
    /// 退避倍数
    pub backoff_multiplier: f64,
    /// 启用抖动
    pub enable_jitter: bool,
}

impl Default for RetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            initial_delay_ms: 100,
            max_delay_ms: 5000,
            backoff_multiplier: 2.0,
            enable_jitter: true,
        }
    }
}

/// 数据流事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataFlowEvent {
    /// 事件ID
    pub event_id: String,
    /// 事件类型
    pub event_type: DataFlowEventType,
    /// 源模块
    pub source_module: IntegrationModule,
    /// 目标模块
    pub target_module: Option<IntegrationModule>,
    /// 事件数据
    pub data: DataFlowEventData,
    /// 事件时间戳
    pub timestamp: DateTime<Utc>,
    /// 处理优先级
    pub priority: EventPriority,
    /// 处理状态
    pub status: EventProcessingStatus,
    /// 重试次数
    pub retry_count: u32,
}

/// 数据流事件类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DataFlowEventType {
    /// 池状态更新
    PoolStateUpdate,
    /// 价格更新
    PriceUpdate,
    /// 交易事件
    TransactionEvent,
    /// 质量评估
    QualityAssessment,
    /// 系统事件
    SystemEvent,
    /// 错误事件
    ErrorEvent,
    /// 健康检查
    HealthCheck,
    /// 配置变更
    ConfigurationChange,
}

/// 数据流事件数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DataFlowEventData {
    /// 价格报价
    PriceQuote(PriceQuote),
    /// 价格更新事件
    PriceUpdate(PriceUpdateEvent),
    /// 池状态变更
    PoolStateChange {
        pool_id: String,
        dex_type: DexType,
        old_state: Option<serde_json::Value>,
        new_state: serde_json::Value,
    },
    /// 交易数据
    Transaction {
        signature: String,
        slot: u64,
        pool_id: String,
        dex_type: DexType,
        transaction_data: serde_json::Value,
    },
    /// 质量评估结果
    QualityAssessment {
        token_pair: TokenPair,
        dex_type: DexType,
        quality_score: f64,
        assessment_data: serde_json::Value,
    },
    /// 系统状态
    SystemStatus {
        module: IntegrationModule,
        status: String,
        metrics: HashMap<String, serde_json::Value>,
    },
    /// 错误信息
    Error {
        error_type: String,
        error_message: String,
        error_data: Option<serde_json::Value>,
    },
    /// 原始数据
    Raw(serde_json::Value),
}

/// 事件优先级
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum EventPriority {
    /// 低优先级
    Low,
    /// 正常优先级
    Normal,
    /// 高优先级
    High,
    /// 紧急优先级
    Critical,
}

impl Default for EventPriority {
    fn default() -> Self {
        EventPriority::Normal
    }
}

/// 事件处理状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum EventProcessingStatus {
    /// 待处理
    Pending,
    /// 处理中
    Processing,
    /// 处理成功
    Completed,
    /// 处理失败
    Failed,
    /// 已取消
    Cancelled,
    /// 需要重试
    Retry,
}

/// 数据流处理器接口
#[async_trait]
pub trait DataFlowProcessor: Send + Sync + std::fmt::Debug {
    /// 处理数据流事件
    async fn process_event(&self, event: &DataFlowEvent) -> Result<(), IntegrationError>;

    /// 批量处理事件
    async fn process_batch(&self, events: &[DataFlowEvent]) -> Result<Vec<Result<(), IntegrationError>>, IntegrationError> {
        let mut results = Vec::with_capacity(events.len());
        for event in events {
            results.push(self.process_event(event).await);
        }
        Ok(results)
    }

    /// 获取处理器名称
    fn processor_name(&self) -> &'static str;

    /// 获取支持的事件类型
    fn supported_event_types(&self) -> Vec<DataFlowEventType>;

    /// 健康检查
    async fn health_check(&self) -> Result<bool, IntegrationError>;

    /// 获取处理统计
    async fn get_processing_stats(&self) -> Result<ProcessingStats, IntegrationError>;
}

/// 处理统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProcessingStats {
    /// 总处理事件数
    pub total_events: u64,
    /// 成功处理数
    pub successful_events: u64,
    /// 失败处理数
    pub failed_events: u64,
    /// 平均处理时间（毫秒）
    pub average_processing_time_ms: f64,
    /// 最后处理时间
    pub last_processed_at: Option<DateTime<Utc>>,
    /// 错误率
    pub error_rate: f64,
    /// 吞吐量（事件/秒）
    pub throughput_per_second: f64,
}

impl ProcessingStats {
    /// 创建新的处理统计
    pub fn new() -> Self {
        Self {
            total_events: 0,
            successful_events: 0,
            failed_events: 0,
            average_processing_time_ms: 0.0,
            last_processed_at: None,
            error_rate: 0.0,
            throughput_per_second: 0.0,
        }
    }

    /// 记录成功处理
    pub fn record_success(&mut self, processing_time_ms: f64) {
        self.total_events += 1;
        self.successful_events += 1;
        self.update_average_time(processing_time_ms);
        self.update_error_rate();
        self.last_processed_at = Some(Utc::now());
    }

    /// 记录失败处理
    pub fn record_failure(&mut self, processing_time_ms: f64) {
        self.total_events += 1;
        self.failed_events += 1;
        self.update_average_time(processing_time_ms);
        self.update_error_rate();
        self.last_processed_at = Some(Utc::now());
    }

    /// 更新平均处理时间
    fn update_average_time(&mut self, processing_time_ms: f64) {
        self.average_processing_time_ms = (self.average_processing_time_ms * (self.total_events - 1) as f64 
            + processing_time_ms) / self.total_events as f64;
    }

    /// 更新错误率
    fn update_error_rate(&mut self) {
        self.error_rate = if self.total_events > 0 {
            self.failed_events as f64 / self.total_events as f64
        } else {
            0.0
        };
    }

    /// 计算吞吐量
    pub fn calculate_throughput(&mut self, window_seconds: u64) {
        if let Some(last_time) = self.last_processed_at {
            let elapsed_seconds = (Utc::now() - last_time).num_seconds() as u64;
            if elapsed_seconds > 0 && elapsed_seconds <= window_seconds {
                self.throughput_per_second = self.total_events as f64 / elapsed_seconds as f64;
            }
        }
    }
}

impl Default for ProcessingStats {
    fn default() -> Self {
        Self::new()
    }
}

/// 数据流监听器接口
#[async_trait]
pub trait DataFlowListener: Send + Sync + std::fmt::Debug {
    /// 启动监听
    async fn start(&self) -> Result<(), IntegrationError>;

    /// 停止监听
    async fn stop(&self) -> Result<(), IntegrationError>;

    /// 注册事件处理器
    async fn register_processor(&self, processor: Box<dyn DataFlowProcessor>) -> Result<(), IntegrationError>;

    /// 移除事件处理器
    async fn unregister_processor(&self, processor_name: &str) -> Result<(), IntegrationError>;

    /// 发送事件
    async fn send_event(&self, event: DataFlowEvent) -> Result<(), IntegrationError>;

    /// 批量发送事件
    async fn send_batch(&self, events: Vec<DataFlowEvent>) -> Result<(), IntegrationError>;

    /// 获取监听状态
    async fn get_status(&self) -> Result<ListenerStatus, IntegrationError>;
}

/// 监听器状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ListenerStatus {
    /// 是否运行中
    pub is_running: bool,
    /// 注册的处理器数量
    pub processor_count: usize,
    /// 待处理事件数量
    pub pending_events: usize,
    /// 最后活动时间
    pub last_activity: Option<DateTime<Utc>>,
    /// 处理统计
    pub stats: ProcessingStats,
}

/// 重试策略
#[derive(Debug)]
pub struct RetryStrategy {
    config: RetryConfig,
}

impl RetryStrategy {
    /// 创建新的重试策略
    pub fn new(config: RetryConfig) -> Self {
        Self { config }
    }

    /// 计算下次重试延迟
    pub fn calculate_delay(&self, attempt: u32) -> std::time::Duration {
        let base_delay = self.config.initial_delay_ms as f64;
        let multiplier = self.config.backoff_multiplier.powi(attempt as i32);
        let mut delay_ms = base_delay * multiplier;

        // 应用最大延迟限制
        delay_ms = delay_ms.min(self.config.max_delay_ms as f64);

        // 应用抖动
        if self.config.enable_jitter {
            let jitter = rand::random::<f64>() * 0.1; // ±10% 抖动
            delay_ms *= 1.0 + (jitter - 0.05);
        }

        std::time::Duration::from_millis(delay_ms as u64)
    }

    /// 检查是否应该重试
    pub fn should_retry(&self, attempt: u32) -> bool {
        attempt < self.config.max_retries
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use uuid::Uuid;

    #[test]
    fn test_integration_config_default() {
        let config = IntegrationConfig::default();
        assert_eq!(config.event_buffer_size, 1000);
        assert_eq!(config.batch_size, 50);
        assert!(!config.enabled_modules.is_empty());
    }

    #[test]
    fn test_retry_config() {
        let config = RetryConfig::default();
        assert_eq!(config.max_retries, 3);
        assert_eq!(config.initial_delay_ms, 100);
        assert!(config.enable_jitter);
    }

    #[test]
    fn test_retry_strategy() {
        let config = RetryConfig::default();
        let strategy = RetryStrategy::new(config);

        // 第一次重试
        let delay1 = strategy.calculate_delay(1);
        assert!(delay1.as_millis() >= 100);

        // 第二次重试应该更长
        let delay2 = strategy.calculate_delay(2);
        assert!(delay2 > delay1);

        // 检查重试条件
        assert!(strategy.should_retry(0));
        assert!(strategy.should_retry(1));
        assert!(strategy.should_retry(2));
        assert!(!strategy.should_retry(3));
    }

    #[test]
    fn test_processing_stats() {
        let mut stats = ProcessingStats::new();
        
        stats.record_success(100.0);
        stats.record_success(200.0);
        stats.record_failure(150.0);

        assert_eq!(stats.total_events, 3);
        assert_eq!(stats.successful_events, 2);
        assert_eq!(stats.failed_events, 1);
        assert_eq!(stats.error_rate, 1.0 / 3.0);
        assert_eq!(stats.average_processing_time_ms, 150.0);
    }

    #[test]
    fn test_data_flow_event_creation() {
        let event = DataFlowEvent {
            event_id: Uuid::new_v4().to_string(),
            event_type: DataFlowEventType::PriceUpdate,
            source_module: IntegrationModule::StateManager,
            target_module: Some(IntegrationModule::PriceCache),
            data: DataFlowEventData::Raw(serde_json::json!({"test": "data"})),
            timestamp: Utc::now(),
            priority: EventPriority::Normal,
            status: EventProcessingStatus::Pending,
            retry_count: 0,
        };

        assert_eq!(event.event_type, DataFlowEventType::PriceUpdate);
        assert_eq!(event.source_module, IntegrationModule::StateManager);
        assert_eq!(event.priority, EventPriority::Normal);
        assert_eq!(event.status, EventProcessingStatus::Pending);
    }

    #[test]
    fn test_event_priority_ordering() {
        assert!(EventPriority::Critical > EventPriority::High);
        assert!(EventPriority::High > EventPriority::Normal);
        assert!(EventPriority::Normal > EventPriority::Low);
    }

    #[test]
    fn test_integration_module_equality() {
        assert_eq!(IntegrationModule::StateManager, IntegrationModule::StateManager);
        assert_ne!(IntegrationModule::StateManager, IntegrationModule::DataParser);
    }
}